'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function DebugAuthPage() {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  useEffect(() => {
    const getUser = async () => {
      try {
        const { data: { user }, error } = await supabase.auth.getUser()
        
        if (error) {
          setError(error.message)
        } else {
          setUser(user)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    getUser()
  }, [])

  const handleSetPasswordSetupFlag = async () => {
    try {
      const { error } = await supabase.auth.updateUser({
        data: {
          needs_password_setup: true,
        }
      })

      if (error) {
        setError(error.message)
      } else {
        // Refresh user data
        const { data: { user } } = await supabase.auth.getUser()
        setUser(user)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }

  const handleClearPasswordSetupFlag = async () => {
    try {
      const { error } = await supabase.auth.updateUser({
        data: {
          needs_password_setup: false,
        }
      })

      if (error) {
        setError(error.message)
      } else {
        // Refresh user data
        const { data: { user } } = await supabase.auth.getUser()
        setUser(user)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="p-6">
            Loading...
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              <strong>Error:</strong> {error}
            </div>
          )}

          <div>
            <h3 className="font-medium mb-2">User Information:</h3>
            {user ? (
              <div className="bg-gray-50 p-4 rounded space-y-2">
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>Email Confirmed:</strong> {user.email_confirmed_at ? 'Yes' : 'No'}</p>
                <p><strong>Created:</strong> {user.created_at}</p>
                <p><strong>Last Sign In:</strong> {user.last_sign_in_at}</p>
                <p><strong>User Metadata:</strong></p>
                <pre className="bg-white p-2 rounded text-sm overflow-auto">
                  {JSON.stringify(user.user_metadata, null, 2)}
                </pre>
                <p><strong>App Metadata:</strong></p>
                <pre className="bg-white p-2 rounded text-sm overflow-auto">
                  {JSON.stringify(user.app_metadata, null, 2)}
                </pre>
              </div>
            ) : (
              <p className="text-gray-600">No user authenticated</p>
            )}
          </div>

          {user && (
            <div className="space-y-2">
              <h3 className="font-medium">Test Actions:</h3>
              <div className="space-x-2">
                <Button 
                  onClick={handleSetPasswordSetupFlag}
                  variant="outline"
                >
                  Set Password Setup Flag
                </Button>
                <Button 
                  onClick={handleClearPasswordSetupFlag}
                  variant="outline"
                >
                  Clear Password Setup Flag
                </Button>
                <Button 
                  onClick={() => window.location.href = '/setup-password'}
                  className="bg-orange-600 hover:bg-orange-700"
                >
                  Go to Setup Password
                </Button>
              </div>
            </div>
          )}

          <div>
            <h3 className="font-medium mb-2">Current Path Information:</h3>
            <div className="bg-gray-50 p-4 rounded space-y-1">
              <p><strong>Current URL:</strong> {window.location.href}</p>
              <p><strong>Pathname:</strong> {window.location.pathname}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 