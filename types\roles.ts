export interface Permission {
  resource: string;
  action: string;
  scope?: 'own' | 'team' | 'organization';
}

export interface Role {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  permissions: Permission[];
  isSystemRole: boolean;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserRole {
  id: string;
  userId: string;
  roleId: string;
  organizationId: string;
  assignedBy?: string;
  assignedAt: string;
  expiresAt?: string;
  isActive?: boolean;
  role?: Role; // Joined data
}

export interface RoleAuditEntry {
  id: string;
  userId: string;
  targetUserId?: string;
  roleId?: string;
  organizationId: string;
  action: 'assigned' | 'removed' | 'modified' | 'role_created' | 'role_updated' | 'role_deleted';
  oldPermissions?: Permission[];
  newPermissions?: Permission[];
  metadata?: Record<string, any>;
  createdAt: string;
}

export interface TeamInvitation {
  id: string;
  organizationId: string;
  email: string;
  roleId?: string;
  invitedBy: string;
  expiresAt: string;
  acceptedAt?: string;
  status: 'pending' | 'accepted' | 'expired' | 'cancelled';
  token: string;
  createdAt: string;
  updatedAt: string;
  role?: Role; // Joined data
  inviter?: {
    id: string;
    fullName?: string;
    email: string;
  };
}

export interface TeamMember {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  role: 'owner' | 'admin' | 'manager' | 'staff';
  isActive: boolean;
  lastLoginAt?: string;
  invitedBy?: string;
  invitedAt?: string;
  acceptedAt?: string;
  createdAt: string;
  assignedRoles?: UserRole[];
  // Enhanced staff information
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  emergencyContact?: {
    name: string;
    phoneNumber: string;
    relationship: string;
  };
  salary?: {
    amount: number;
    currency: string;
    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly';
  };
  employmentType?: 'full-time' | 'part-time' | 'contract' | 'intern';
  department?: string;
  position?: string;
  startDate?: string;
  endDate?: string;
  notes?: string;
}

export interface CreateRoleData {
  name: string;
  description?: string;
  permissions: Permission[];
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  permissions?: Permission[];
}

export interface AssignRoleData {
  userId: string;
  roleId: string;
  expiresAt?: string;
}

export interface InviteTeamMemberData {
  email: string;
  roleId?: string;
  message?: string;
}

export interface EnhancedInviteTeamMemberData {
  email: string;
  fullName?: string;
  roleId?: string;
  message?: string;
  
  // Contact information
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  
  // Emergency contact
  emergencyContact?: {
    name: string;
    phoneNumber: string;
    relationship: string;
  };
  
  // Employment information
  position?: string;
  department?: string;
  employmentType?: 'full-time' | 'part-time' | 'contract' | 'intern';
  startDate?: string;
  endDate?: string;
  
  // Salary information
  salary?: {
    amount: number;
    currency: string;
    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly';
  };
  
  // Additional information
  notes?: string;
}

export interface CreateTeamMemberData {
  email: string;
  fullName?: string;
  roleId?: string;
  
  // Contact information
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  
  // Emergency contact
  emergencyContact?: {
    name: string;
    phoneNumber: string;
    relationship: string;
  };
  
  // Employment information
  position?: string;
  department?: string;
  employmentType?: 'full-time' | 'part-time' | 'contract' | 'intern';
  startDate?: string;
  endDate?: string;
  
  // Salary information
  salary?: {
    amount: number;
    currency: string;
    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly';
  };
  
  // Additional information
  notes?: string;
}

export interface CreatedTeamMember {
  id: string;
  email: string;
  fullName?: string;
  organizationId: string;
  role: 'owner' | 'admin' | 'manager' | 'staff';
  isActive: boolean;
  needsPasswordSetup: boolean;
  assignedRoles?: UserRole[];
  createdBy: string;
  createdAt: string;
  temporaryPassword?: string; // Only returned once during creation
}

// Permission constants
export const PERMISSIONS = {
  // Menu management
  MENU_CREATE: { resource: 'menu', action: 'create' },
  MENU_READ: { resource: 'menu', action: 'read' },
  MENU_UPDATE: { resource: 'menu', action: 'update' },
  MENU_DELETE: { resource: 'menu', action: 'delete' },
  MENU_PUBLISH: { resource: 'menu', action: 'publish' },
  MENU_MANAGE: { resource: 'menu', action: 'manage' },

  // Organization management
  ORG_MANAGE: { resource: 'organization', action: 'manage' },
  ORG_SETTINGS: { resource: 'organization', action: 'settings' },
  ORG_BILLING: { resource: 'organization', action: 'billing' },
  ORG_VIEW: { resource: 'organization', action: 'view' },

  // User management
  USER_INVITE: { resource: 'user', action: 'invite' },
  USER_CREATE: { resource: 'user', action: 'create' },
  USER_MANAGE: { resource: 'user', action: 'manage' },
  USER_REMOVE: { resource: 'user', action: 'remove' },
  USER_VIEW: { resource: 'user', action: 'view' },

  // Role management
  ROLE_CREATE: { resource: 'role', action: 'create' },
  ROLE_READ: { resource: 'role', action: 'read' },
  ROLE_UPDATE: { resource: 'role', action: 'update' },
  ROLE_DELETE: { resource: 'role', action: 'delete' },
  ROLE_ASSIGN: { resource: 'role', action: 'assign' },

  // Analytics
  ANALYTICS_READ: { resource: 'analytics', action: 'read' },
  ANALYTICS_EXPORT: { resource: 'analytics', action: 'export' },
  ANALYTICS_MANAGE: { resource: 'analytics', action: 'manage' },

  // Settings
  SETTINGS_VIEW: { resource: 'settings', action: 'view' },
  SETTINGS_MANAGE: { resource: 'settings', action: 'manage' },
} as const;

// Default system roles
export const SYSTEM_ROLES = {
  OWNER: {
    name: 'Owner',
    description: 'Full access to all organization features',
    permissions: [
      PERMISSIONS.MENU_MANAGE,
      PERMISSIONS.ORG_MANAGE,
      PERMISSIONS.USER_MANAGE,
      PERMISSIONS.USER_CREATE,
      PERMISSIONS.ROLE_CREATE,
      PERMISSIONS.ROLE_READ,
      PERMISSIONS.ROLE_UPDATE,
      PERMISSIONS.ROLE_DELETE,
      PERMISSIONS.ROLE_ASSIGN,
      PERMISSIONS.ANALYTICS_MANAGE,
      PERMISSIONS.SETTINGS_MANAGE,
    ] as Permission[],
    isSystemRole: true,
  },
  MANAGER: {
    name: 'Manager',
    description: 'Manage menus and staff, view analytics',
    permissions: [
      PERMISSIONS.MENU_CREATE,
      PERMISSIONS.MENU_READ,
      PERMISSIONS.MENU_UPDATE,
      PERMISSIONS.MENU_DELETE,
      PERMISSIONS.MENU_PUBLISH,
      PERMISSIONS.USER_INVITE,
      PERMISSIONS.USER_CREATE,
      PERMISSIONS.USER_VIEW,
      PERMISSIONS.ROLE_READ,
      PERMISSIONS.ANALYTICS_READ,
      PERMISSIONS.SETTINGS_VIEW,
    ] as Permission[],
    isSystemRole: true,
  },
  STAFF: {
    name: 'Staff',
    description: 'Edit menus and view basic information',
    permissions: [
      PERMISSIONS.MENU_READ,
      PERMISSIONS.MENU_UPDATE,
      PERMISSIONS.USER_VIEW,
      PERMISSIONS.SETTINGS_VIEW,
    ] as Permission[],
    isSystemRole: true,
  },
  VIEWER: {
    name: 'Viewer',
    description: 'Read-only access to menus and analytics',
    permissions: [
      PERMISSIONS.MENU_READ,
      PERMISSIONS.USER_VIEW,
      PERMISSIONS.ANALYTICS_READ,
      PERMISSIONS.SETTINGS_VIEW,
    ] as Permission[],
    isSystemRole: true,
  },
} as const;

export type SystemRoleName = keyof typeof SYSTEM_ROLES;

// Role hierarchy for permission checking
export const ROLE_HIERARCHY = {
  owner: 4,
  admin: 3,
  manager: 2,
  staff: 1,
  viewer: 0,
} as const;

export type RoleLevel = keyof typeof ROLE_HIERARCHY;

// Helper types
export type PermissionAction = typeof PERMISSIONS[keyof typeof PERMISSIONS]['action'];
export type PermissionResource = typeof PERMISSIONS[keyof typeof PERMISSIONS]['resource']; 