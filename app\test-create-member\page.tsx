'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertTriangle } from 'lucide-react';

export default function TestCreateMemberPage() {
  const [email, setEmail] = useState('');
  const [fullName, setFullName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleCreateMember = async () => {
    if (!email) {
      setError('Email is required');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/rbac/team/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId: 'test-org-id', // You'll need to replace this with a real org ID
          email,
          fullName,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'Failed to create team member');
        return;
      }

      setResult(data);
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Test Create Team Member</CardTitle>
          <CardDescription>
            Test the direct team member creation functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Email</label>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Full Name</label>
            <Input
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              placeholder="John Doe"
            />
          </div>

          <Button 
            onClick={handleCreateMember}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? 'Creating...' : 'Create Team Member'}
          </Button>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {result && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p><strong>Success!</strong> Team member created:</p>
                  <p><strong>Email:</strong> {result.member?.email}</p>
                  <p><strong>ID:</strong> {result.member?.id}</p>
                  <p><strong>Temporary Password:</strong> {result.member?.temporaryPassword}</p>
                  <p className="text-sm text-gray-600">
                    The user should login with this temporary password and will be prompted to change it.
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="text-sm text-gray-600">
            <p><strong>Note:</strong> This is a test page. In production:</p>
            <ul className="list-disc list-inside space-y-1 mt-2">
              <li>The organization ID would be automatically detected</li>
              <li>Proper permissions would be checked</li>
              <li>The temporary password would be securely communicated</li>
              <li>The user would be redirected to setup their password on first login</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 