import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { createTeamMember } from '@/lib/db/rbac-queries';
import { createTeamMemberSchema } from '@/lib/validations/rbac';
import { requirePermission } from '@/lib/auth/rbac';
import { PERMISSIONS } from '@/types/roles';

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { organizationId, ...teamMemberData } = body;

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    // Check permission to create users
    await requirePermission(PERMISSIONS.USER_CREATE, organizationId);

    // Validate team member data
    const result = createTeamMemberSchema.safeParse(teamMemberData);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid team member data', details: result.error.errors },
        { status: 400 }
      );
    }

    const createdMember = await createTeamMember(organizationId, result.data, user.id);

    // Return success response (temporary password will be shown once)
    return NextResponse.json({ 
      member: createdMember,
      message: 'Team member created successfully'
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error creating team member:', error);
    
    if (error.name === 'PermissionError') {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }
    
    if (error.code === 'AUTH_ERROR') {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    if (error.code === '23505') { // Unique constraint violation
      return NextResponse.json({ error: 'A user with this email already exists' }, { status: 409 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 