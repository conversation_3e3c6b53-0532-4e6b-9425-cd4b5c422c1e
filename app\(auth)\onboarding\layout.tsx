'use client'

import { Suspense } from "react"
import { AuthProvider } from "@/lib/contexts/auth-context"

export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-gray-50">
        <Suspense fallback={
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center space-y-4">
              <div className="h-8 w-8 border-4 border-orange-200 border-t-orange-600 rounded-full animate-spin mx-auto"></div>
              <p className="text-gray-600">Loading...</p>
            </div>
          </div>
        }>
          {children}
        </Suspense>
      </div>
    </AuthProvider>
  )
} 