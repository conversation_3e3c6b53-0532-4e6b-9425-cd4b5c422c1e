'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreVertical,
  Edit,
  Trash2,
  Shield,
  Eye,
  Sparkles,
  Users,
  Crown,
  Star,
  Zap
} from 'lucide-react';
import { Role, Permission, PERMISSIONS } from '@/types/roles';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import QuickRoleCreator from '@/components/roles/quick-role-creator';

export default function RoleManagementPage() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [organizationId, setOrganizationId] = useState<string>('');
  
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isQuickCreateOpen, setIsQuickCreateOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  const { toast } = useToast();

  const loadRoles = useCallback(async () => {
    try {
      setLoading(true);
      
      // Get current user's organization
      const response = await fetch('/api/auth/user');
      const userData = await response.json();
      const orgId = userData.user?.organization_id;
      
      if (!orgId) {
        toast({
          title: 'Error',
          description: 'No organization found',
          variant: 'destructive',
        });
        return;
      }
      
      setOrganizationId(orgId);

      // Load only custom roles
      const rolesRes = await fetch(`/api/rbac/roles?organizationId=${orgId}`);
      if (rolesRes.ok) {
        const rolesData = await rolesRes.json();
        // Filter out system roles completely
        setRoles((rolesData.roles || []).filter((role: Role) => !role.isSystemRole));
      }
    } catch (error) {
      console.error('Error loading roles:', error);
      setError('Failed to load roles');
      toast({
        title: 'Error',
        description: 'Failed to load roles',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadRoles();
  }, [loadRoles]);

  // Create/Edit role form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissions: [] as Permission[],
  });

  // Track "none" selections for each resource
  const [noneSelections, setNoneSelections] = useState<Record<string, boolean>>({});

  // Available permissions grouped by resource
  const permissionGroups = {
    menu: [
      PERMISSIONS.MENU_READ,
      PERMISSIONS.MENU_CREATE,
      PERMISSIONS.MENU_UPDATE,
      PERMISSIONS.MENU_DELETE,
      PERMISSIONS.MENU_PUBLISH,
      PERMISSIONS.MENU_MANAGE,
    ],
    user: [
      PERMISSIONS.USER_VIEW,
      PERMISSIONS.USER_INVITE,
      PERMISSIONS.USER_MANAGE,
      PERMISSIONS.USER_REMOVE,
    ],
    role: [
      PERMISSIONS.ROLE_READ,
      PERMISSIONS.ROLE_CREATE,
      PERMISSIONS.ROLE_UPDATE,
      PERMISSIONS.ROLE_DELETE,
      PERMISSIONS.ROLE_ASSIGN,
    ],
    organization: [
      PERMISSIONS.ORG_VIEW,
      PERMISSIONS.ORG_MANAGE,
      PERMISSIONS.ORG_BILLING,
      PERMISSIONS.ORG_SETTINGS,
    ],
    analytics: [
      PERMISSIONS.ANALYTICS_READ,
      PERMISSIONS.ANALYTICS_EXPORT,
      PERMISSIONS.ANALYTICS_MANAGE,
    ],
    settings: [
      PERMISSIONS.SETTINGS_VIEW,
      PERMISSIONS.SETTINGS_MANAGE,
    ],
  };

  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getPermissionCount = (permissions: Permission[]) => {
    return permissions.length;
  };

  const resetForm = () => {
    setFormData({ name: '', description: '', permissions: [] });
    setNoneSelections({});
  };

  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.permissions.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select at least one permission',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      const response = await fetch('/api/rbac/roles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId,
          ...formData,
        }),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Role created successfully',
        });
        setIsCreateDialogOpen(false);
        resetForm();
        loadRoles();
      } else {
        const error = await response.json();
        toast({
          title: 'Error',
          description: error.error || 'Failed to create role',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating role:', error);
      toast({
        title: 'Error',
        description: 'Failed to create role',
        variant: 'destructive',
      });
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedRole || formData.permissions.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select at least one permission',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      const response = await fetch(`/api/rbac/roles/${selectedRole.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId,
          ...formData,
        }),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Role updated successfully',
        });
        setIsEditDialogOpen(false);
        resetForm();
        setSelectedRole(null);
        loadRoles();
      } else {
        const error = await response.json();
        toast({
          title: 'Error',
          description: error.error || 'Failed to update role',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating role:', error);
      toast({
        title: 'Error',
        description: 'Failed to update role',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    if (!confirm('Are you sure you want to delete this role? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/rbac/roles/${roleId}?organizationId=${organizationId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Role deleted successfully',
        });
        loadRoles();
      } else {
        const error = await response.json();
        toast({
          title: 'Error',
          description: error.error || 'Failed to delete role',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error deleting role:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete role',
        variant: 'destructive',
      });
    }
  };

  const handleViewDetails = (role: Role) => {
    setSelectedRole(role);
    setIsViewDialogOpen(true);
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setFormData({
      name: role.name,
      description: role.description || '',
      permissions: role.permissions,
    });
    setIsEditDialogOpen(true);
  };

  const handlePermissionToggle = (permission: Permission, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked
        ? [...prev.permissions, permission]
        : prev.permissions.filter(p => 
            p.resource !== permission.resource || p.action !== permission.action
          )
    }));

    // If any permission is selected, uncheck "none" for that resource
    if (checked) {
      setNoneSelections(prev => ({
        ...prev,
        [permission.resource]: false
      }));
    }
  };

  const handleNoneToggle = (resource: string, checked: boolean) => {
    setNoneSelections(prev => ({
      ...prev,
      [resource]: checked
    }));

    // If "none" is selected, remove all permissions for that resource
    if (checked) {
      setFormData(prev => ({
        ...prev,
        permissions: prev.permissions.filter(p => p.resource !== resource)
      }));
    }
  };

  const isPermissionSelected = (permission: Permission) => {
    return formData.permissions.some(p => 
      p.resource === permission.resource && p.action === permission.action
    );
  };

  const getPermissionBadgeColor = (permission: Permission) => {
    switch (permission.resource) {
      case 'menu': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'user': return 'bg-green-100 text-green-800 border-green-200';
      case 'role': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'organization': return 'bg-red-100 text-red-800 border-red-200';
      case 'analytics': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'settings': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleIcon = (index: number) => {
    const icons = [Crown, Star, Zap, Shield, Sparkles, Users];
    return icons[index % icons.length];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-8">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              Role Management
            </h1>
            <p className="text-gray-600 text-lg">
              Create and manage custom roles with specific permissions for your team
            </p>
          </div>
          <div className="flex gap-3">
            <Button 
              onClick={() => setIsQuickCreateOpen(true)} 
              variant="outline"
              className="border-orange-200 text-orange-600 hover:bg-orange-50 hover:border-orange-300 shadow-lg hover:shadow-xl transition-all duration-200 gap-2"
              size="lg"
            >
              <Zap className="h-5 w-5" />
              Quick Role
            </Button>
            <Button 
              onClick={() => {
                resetForm();
                setIsCreateDialogOpen(true);
              }} 
              className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 gap-2"
              size="lg"
            >
              <Plus className="h-5 w-5" />
              Create New Role
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="Search roles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white/50 backdrop-blur-sm border-gray-200 focus:border-orange-500 focus:ring-orange-500"
            />
          </div>
        </div>

        {/* Roles Grid */}
        {filteredRoles.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gradient-to-br from-orange-100 to-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Shield className="h-12 w-12 text-orange-500" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-3">No roles yet</h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Get started by creating your first custom role with specific permissions for your team members.
            </p>
            <div className="flex gap-3 justify-center">
              <Button 
                onClick={() => setIsQuickCreateOpen(true)} 
                variant="outline"
                className="border-orange-200 text-orange-600 hover:bg-orange-50 hover:border-orange-300 shadow-lg hover:shadow-xl transition-all duration-200 gap-2"
                size="lg"
              >
                <Zap className="h-5 w-5" />
                Quick Role
              </Button>
              <Button 
                onClick={() => {
                  resetForm();
                  setIsCreateDialogOpen(true);
                }} 
                className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 gap-2"
                size="lg"
              >
                <Plus className="h-5 w-5" />
                Create Your First Role
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredRoles.map((role, index) => {
              const IconComponent = getRoleIcon(index);
              return (
                <Card key={role.id} className="group hover:shadow-2xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm hover:bg-white/90 hover:scale-105">
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gradient-to-br from-orange-100 to-red-100 rounded-xl flex items-center justify-center group-hover:from-orange-200 group-hover:to-red-200 transition-colors">
                          <IconComponent className="h-6 w-6 text-orange-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-orange-600 transition-colors">
                            {role.name}
                          </CardTitle>
                          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 mt-1">
                            Custom Role
                          </Badge>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem onClick={() => handleViewDetails(role)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditRole(role)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Role
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteRole(role.id)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Role
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {role.description && (
                      <p className="text-sm text-gray-600 leading-relaxed">{role.description}</p>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-500">Permissions</span>
                      <Badge variant="secondary" className="bg-gray-100 text-gray-700">
                        {getPermissionCount(role.permissions)} permissions
                      </Badge>
                    </div>

                    <div className="flex flex-wrap gap-1.5">
                      {role.permissions.slice(0, 4).map((permission, permIndex) => (
                        <Badge
                          key={permIndex}
                          className={`${getPermissionBadgeColor(permission)} text-xs font-medium border`}
                          variant="secondary"
                        >
                          {permission.action}
                        </Badge>
                      ))}
                      {role.permissions.length > 4 && (
                        <Badge variant="outline" className="text-xs bg-gray-50">
                          +{role.permissions.length - 4} more
                        </Badge>
                      )}
                    </div>

                    <div className="flex gap-2 pt-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex-1 gap-2 hover:bg-orange-50 hover:border-orange-200 hover:text-orange-600 transition-colors"
                        onClick={() => handleViewDetails(role)}
                      >
                        <Eye className="h-4 w-4" />
                        View Details
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="gap-2 hover:bg-orange-50 hover:border-orange-200 hover:text-orange-600 transition-colors"
                        onClick={() => handleEditRole(role)}
                      >
                        <Edit className="h-4 w-4" />
                        Edit
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Create Role Dialog */}
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="sm:max-w-3xl max-h-[85vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold text-gray-900">Create New Role</DialogTitle>
              <DialogDescription className="text-gray-600">
                Define a custom role with specific permissions for your team members
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreateSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="create-name" className="text-sm font-semibold">Role Name</Label>
                  <Input
                    id="create-name"
                    placeholder="e.g., Content Manager"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label htmlFor="create-description" className="text-sm font-semibold">Description</Label>
                  <Input
                    id="create-description"
                    placeholder="Brief description of the role"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="mt-1"
                  />
                </div>
              </div>
              
              <div>
                <Label className="text-lg font-semibold text-gray-900">Permissions</Label>
                <p className="text-sm text-gray-600 mb-4">
                  Select the permissions this role should have
                </p>
                
                <div className="grid gap-4 md:grid-cols-2">
                  {Object.entries(permissionGroups).map(([resource, permissions]) => (
                    <div key={resource} className="border rounded-xl p-4 bg-gray-50/50">
                      <h4 className="font-semibold capitalize text-gray-900 mb-3 flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${getPermissionBadgeColor(permissions[0]).split(' ')[0]}`}></div>
                        {resource} Management
                      </h4>
                      <div className="space-y-3">
                        {/* None option */}
                        <div className="flex items-center space-x-2 pb-2 border-b border-gray-200">
                          <Checkbox
                            id={`create-${resource}.none`}
                            checked={noneSelections[resource] || false}
                            onCheckedChange={(checked) => 
                              handleNoneToggle(resource, checked as boolean)
                            }
                          />
                          <Label 
                            htmlFor={`create-${resource}.none`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-red-600"
                          >
                            No Access
                          </Label>
                        </div>
                        
                        {/* Permission options */}
                        <div className="space-y-2">
                          {permissions.map((permission) => (
                            <div key={`${permission.resource}.${permission.action}`} className="flex items-center space-x-2">
                              <Checkbox
                                id={`create-${permission.resource}.${permission.action}`}
                                checked={isPermissionSelected(permission)}
                                disabled={noneSelections[resource]}
                                onCheckedChange={(checked) => 
                                  handlePermissionToggle(permission, checked as boolean)
                                }
                              />
                              <Label 
                                htmlFor={`create-${permission.resource}.${permission.action}`}
                                className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${
                                  noneSelections[resource] ? 'text-gray-400' : ''
                                }`}
                              >
                                {permission.action.charAt(0).toUpperCase() + permission.action.slice(1)}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsCreateDialogOpen(false);
                    resetForm();
                  }}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white"
                >
                  Create Role
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* Edit Role Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-3xl max-h-[85vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold text-gray-900">Edit Role</DialogTitle>
              <DialogDescription className="text-gray-600">
                Modify the permissions and details for this role
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleEditSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-name" className="text-sm font-semibold">Role Name</Label>
                  <Input
                    id="edit-name"
                    placeholder="e.g., Content Manager"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label htmlFor="edit-description" className="text-sm font-semibold">Description</Label>
                  <Input
                    id="edit-description"
                    placeholder="Brief description of the role"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="mt-1"
                  />
                </div>
              </div>
              
              <div>
                <Label className="text-lg font-semibold text-gray-900">Permissions</Label>
                <p className="text-sm text-gray-600 mb-4">
                  Select the permissions this role should have
                </p>
                
                <div className="grid gap-4 md:grid-cols-2">
                  {Object.entries(permissionGroups).map(([resource, permissions]) => (
                    <div key={resource} className="border rounded-xl p-4 bg-gray-50/50">
                      <h4 className="font-semibold capitalize text-gray-900 mb-3 flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${getPermissionBadgeColor(permissions[0]).split(' ')[0]}`}></div>
                        {resource} Management
                      </h4>
                      <div className="space-y-3">
                        {/* None option */}
                        <div className="flex items-center space-x-2 pb-2 border-b border-gray-200">
                          <Checkbox
                            id={`edit-${resource}.none`}
                            checked={noneSelections[resource] || false}
                            onCheckedChange={(checked) => 
                              handleNoneToggle(resource, checked as boolean)
                            }
                          />
                          <Label 
                            htmlFor={`edit-${resource}.none`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-red-600"
                          >
                            No Access
                          </Label>
                        </div>
                        
                        {/* Permission options */}
                        <div className="space-y-2">
                          {permissions.map((permission) => (
                            <div key={`${permission.resource}.${permission.action}`} className="flex items-center space-x-2">
                              <Checkbox
                                id={`edit-${permission.resource}.${permission.action}`}
                                checked={isPermissionSelected(permission)}
                                disabled={noneSelections[resource]}
                                onCheckedChange={(checked) => 
                                  handlePermissionToggle(permission, checked as boolean)
                                }
                              />
                              <Label 
                                htmlFor={`edit-${permission.resource}.${permission.action}`}
                                className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${
                                  noneSelections[resource] ? 'text-gray-400' : ''
                                }`}
                              >
                                {permission.action.charAt(0).toUpperCase() + permission.action.slice(1)}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsEditDialogOpen(false);
                    resetForm();
                    setSelectedRole(null);
                  }}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white"
                >
                  Update Role
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* View Details Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold text-gray-900">Role Details</DialogTitle>
              <DialogDescription className="text-gray-600">
                Complete overview of this role and its permissions
              </DialogDescription>
            </DialogHeader>
            {selectedRole && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-4">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{selectedRole.name}</h3>
                  {selectedRole.description && (
                    <p className="text-gray-600">{selectedRole.description}</p>
                  )}
                  <div className="mt-3">
                    <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                      Custom Role • {selectedRole.permissions.length} permissions
                    </Badge>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Permissions</h4>
                  <div className="grid gap-3 md:grid-cols-2">
                    {Object.entries(permissionGroups).map(([resource, availablePermissions]) => {
                      const resourcePermissions = selectedRole.permissions.filter(p => p.resource === resource);
                      if (resourcePermissions.length === 0) return null;
                      
                      return (
                        <div key={resource} className="border rounded-lg p-3 bg-gray-50/50">
                          <h5 className="font-medium capitalize text-gray-900 mb-2 flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${getPermissionBadgeColor(availablePermissions[0]).split(' ')[0]}`}></div>
                            {resource}
                          </h5>
                          <div className="flex flex-wrap gap-1">
                            {resourcePermissions.map((permission, index) => (
                              <Badge
                                key={index}
                                className={`${getPermissionBadgeColor(permission)} text-xs font-medium border`}
                                variant="secondary"
                              >
                                {permission.action}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setIsViewDialogOpen(false)}
                  >
                    Close
                  </Button>
                  <Button 
                    onClick={() => {
                      setIsViewDialogOpen(false);
                      handleEditRole(selectedRole);
                    }}
                    className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white gap-2"
                  >
                    <Edit className="h-4 w-4" />
                    Edit Role
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Quick Role Creator Dialog */}
        <Dialog open={isQuickCreateOpen} onOpenChange={setIsQuickCreateOpen}>
          <DialogContent className="sm:max-w-5xl max-h-[90vh] overflow-y-auto p-6">
            <QuickRoleCreator
              organizationId={organizationId}
              onSuccess={() => {
                setIsQuickCreateOpen(false);
                loadRoles();
              }}
              onCancel={() => setIsQuickCreateOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}

 