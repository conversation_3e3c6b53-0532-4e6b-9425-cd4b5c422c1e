'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { DollarSign, MapPin, Phone, User, Briefcase } from 'lucide-react';
import { Role } from '@/types/roles';

interface EnhancedInviteFormProps {
  organizationId: string;
  roles: Role[];
  onSuccess: () => void;
  onCancel: () => void;
}

interface StaffFormData {
  // Basic information
  email: string;
  fullName: string;
  roleId: string;
  message: string;
  
  // Contact information
  phoneNumber: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  
  // Employment information
  position: string;
  department: string;
  employmentType: 'full-time' | 'part-time' | 'contract' | 'intern' | '';
  startDate: string;
  endDate: string;
  
  // Salary information
  salaryAmount: string;
  salaryCurrency: string;
  salaryFrequency: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | '';
  
  // Emergency contact
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelationship: string;
  
  // Additional information
  dateOfBirth: string;
  notes: string;
}

export default function EnhancedInviteForm({ organizationId, roles, onSuccess, onCancel }: EnhancedInviteFormProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  
  const [formData, setFormData] = useState<StaffFormData>({
    email: '',
    fullName: '',
    roleId: '',
    message: '',
    phoneNumber: '',
    address: '',
    city: '',
    state: '',
    postalCode: '',
    country: '',
    position: '',
    department: '',
    employmentType: '',
    startDate: '',
    endDate: '',
    salaryAmount: '',
    salaryCurrency: 'USD',
    salaryFrequency: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    dateOfBirth: '',
    notes: '',
  });

  const updateField = (field: keyof StaffFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.email || !formData.roleId) {
      toast({
        title: 'Validation Error',
        description: 'Email and role are required fields',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    
    try {
      // Prepare the enhanced staff data
      const staffData = {
        organizationId,
        email: formData.email,
        fullName: formData.fullName || undefined,
        roleId: formData.roleId,
        message: formData.message || undefined,
        
        // Enhanced staff information
        phoneNumber: formData.phoneNumber || undefined,
        dateOfBirth: formData.dateOfBirth || undefined,
        address: formData.address || undefined,
        city: formData.city || undefined,
        state: formData.state || undefined,
        postalCode: formData.postalCode || undefined,
        country: formData.country || undefined,
        
        emergencyContact: (formData.emergencyContactName && formData.emergencyContactPhone) ? {
          name: formData.emergencyContactName,
          phoneNumber: formData.emergencyContactPhone,
          relationship: formData.emergencyContactRelationship || 'Not specified',
        } : undefined,
        
        salary: (formData.salaryAmount && formData.salaryFrequency) ? {
          amount: parseFloat(formData.salaryAmount),
          currency: formData.salaryCurrency,
          frequency: formData.salaryFrequency,
        } : undefined,
        
        employmentType: formData.employmentType || undefined,
        department: formData.department || undefined,
        position: formData.position || undefined,
        startDate: formData.startDate || undefined,
        endDate: formData.endDate || undefined,
        notes: formData.notes || undefined,
      };

      const response = await fetch('/api/rbac/team/enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(staffData),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Team member invited successfully with detailed information',
        });
        onSuccess();
      } else {
        const error = await response.json();
        toast({
          title: 'Error',
          description: error.error || 'Failed to send invitation',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error sending enhanced invitation:', error);
      toast({
        title: 'Error',
        description: 'Failed to send invitation',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Invite New Team Member
        </CardTitle>
        <CardDescription>
          Add a new team member with comprehensive staff information
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="contact">Contact</TabsTrigger>
            <TabsTrigger value="employment">Employment</TabsTrigger>
            <TabsTrigger value="additional">Additional</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email" className="text-sm font-medium">
                  Email Address *
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => updateField('email', e.target.value)}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="fullName" className="text-sm font-medium">
                  Full Name
                </Label>
                <Input
                  id="fullName"
                  placeholder="John Doe"
                  value={formData.fullName}
                  onChange={(e) => updateField('fullName', e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="role" className="text-sm font-medium">
                  Role *
                </Label>
                <Select
                  value={formData.roleId}
                  onValueChange={(value) => updateField('roleId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{role.name}</span>
                          {role.isSystemRole ? (
                            <Badge variant="secondary" className="ml-2 text-xs">System</Badge>
                          ) : (
                            <Badge variant="outline" className="ml-2 text-xs">Custom</Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="department" className="text-sm font-medium">
                  Department
                </Label>
                <Input
                  id="department"
                  placeholder="Kitchen, Service, Management"
                  value={formData.department}
                  onChange={(e) => updateField('department', e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="message" className="text-sm font-medium">
                Welcome Message
              </Label>
              <Textarea
                id="message"
                placeholder="Welcome to our team! We're excited to have you join us."
                value={formData.message}
                onChange={(e) => updateField('message', e.target.value)}
                rows={3}
              />
            </div>
          </TabsContent>

          <TabsContent value="contact" className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <MapPin className="h-4 w-4" />
              <h3 className="text-sm font-semibold">Contact Information</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phoneNumber" className="text-sm font-medium">
                  Phone Number
                </Label>
                <Input
                  id="phoneNumber"
                  type="tel"
                  placeholder="+****************"
                  value={formData.phoneNumber}
                  onChange={(e) => updateField('phoneNumber', e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="dateOfBirth" className="text-sm font-medium">
                  Date of Birth
                </Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => updateField('dateOfBirth', e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="address" className="text-sm font-medium">
                Address
              </Label>
              <Input
                id="address"
                placeholder="123 Main Street"
                value={formData.address}
                onChange={(e) => updateField('address', e.target.value)}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="city" className="text-sm font-medium">
                  City
                </Label>
                <Input
                  id="city"
                  placeholder="New York"
                  value={formData.city}
                  onChange={(e) => updateField('city', e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="state" className="text-sm font-medium">
                  State/Province
                </Label>
                <Input
                  id="state"
                  placeholder="NY"
                  value={formData.state}
                  onChange={(e) => updateField('state', e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="postalCode" className="text-sm font-medium">
                  Postal Code
                </Label>
                <Input
                  id="postalCode"
                  placeholder="10001"
                  value={formData.postalCode}
                  onChange={(e) => updateField('postalCode', e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="country" className="text-sm font-medium">
                Country
              </Label>
              <Input
                id="country"
                placeholder="United States"
                value={formData.country}
                onChange={(e) => updateField('country', e.target.value)}
              />
            </div>

            <div className="space-y-4 border-t pt-4">
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <h4 className="text-sm font-semibold">Emergency Contact</h4>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="emergencyContactName" className="text-sm font-medium">
                    Contact Name
                  </Label>
                  <Input
                    id="emergencyContactName"
                    placeholder="Jane Doe"
                    value={formData.emergencyContactName}
                    onChange={(e) => updateField('emergencyContactName', e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="emergencyContactPhone" className="text-sm font-medium">
                    Contact Phone
                  </Label>
                  <Input
                    id="emergencyContactPhone"
                    type="tel"
                    placeholder="+****************"
                    value={formData.emergencyContactPhone}
                    onChange={(e) => updateField('emergencyContactPhone', e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="emergencyContactRelationship" className="text-sm font-medium">
                    Relationship
                  </Label>
                  <Input
                    id="emergencyContactRelationship"
                    placeholder="Spouse, Parent, Sibling"
                    value={formData.emergencyContactRelationship}
                    onChange={(e) => updateField('emergencyContactRelationship', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="employment" className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Briefcase className="h-4 w-4" />
              <h3 className="text-sm font-semibold">Employment Details</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="position" className="text-sm font-medium">
                  Position/Job Title
                </Label>
                <Input
                  id="position"
                  placeholder="Server, Chef, Manager"
                  value={formData.position}
                  onChange={(e) => updateField('position', e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="employmentType" className="text-sm font-medium">
                  Employment Type
                </Label>
                <Select
                  value={formData.employmentType}
                  onValueChange={(value) => updateField('employmentType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select employment type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="full-time">Full-time</SelectItem>
                    <SelectItem value="part-time">Part-time</SelectItem>
                    <SelectItem value="contract">Contract</SelectItem>
                    <SelectItem value="intern">Intern</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate" className="text-sm font-medium">
                  Start Date
                </Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => updateField('startDate', e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="endDate" className="text-sm font-medium">
                  End Date (if applicable)
                </Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => updateField('endDate', e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-4 border-t pt-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                <h4 className="text-sm font-semibold">Salary Information</h4>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="salaryAmount" className="text-sm font-medium">
                    Amount
                  </Label>
                  <Input
                    id="salaryAmount"
                    type="number"
                    placeholder="50000"
                    value={formData.salaryAmount}
                    onChange={(e) => updateField('salaryAmount', e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="salaryCurrency" className="text-sm font-medium">
                    Currency
                  </Label>
                  <Select
                    value={formData.salaryCurrency}
                    onValueChange={(value) => updateField('salaryCurrency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                      <SelectItem value="CAD">CAD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="salaryFrequency" className="text-sm font-medium">
                    Frequency
                  </Label>
                  <Select
                    value={formData.salaryFrequency}
                    onValueChange={(value) => updateField('salaryFrequency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="additional" className="space-y-4">
            <div>
              <Label htmlFor="notes" className="text-sm font-medium">
                Additional Notes
              </Label>
              <Textarea
                id="notes"
                placeholder="Any additional information about the team member..."
                value={formData.notes}
                onChange={(e) => updateField('notes', e.target.value)}
                rows={6}
              />
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between pt-6 border-t">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          
          <div className="flex gap-2">
            {activeTab !== 'basic' && (
              <Button 
                variant="outline" 
                onClick={() => {
                  const tabs = ['basic', 'contact', 'employment', 'additional'];
                  const currentIndex = tabs.indexOf(activeTab);
                  if (currentIndex > 0) setActiveTab(tabs[currentIndex - 1]);
                }}
              >
                Previous
              </Button>
            )}
            
            {activeTab !== 'additional' ? (
              <Button 
                onClick={() => {
                  const tabs = ['basic', 'contact', 'employment', 'additional'];
                  const currentIndex = tabs.indexOf(activeTab);
                  if (currentIndex < tabs.length - 1) setActiveTab(tabs[currentIndex + 1]);
                }}
              >
                Next
              </Button>
            ) : (
              <Button 
                onClick={handleSubmit}
                disabled={loading}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {loading ? 'Sending...' : 'Send Invitation'}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 