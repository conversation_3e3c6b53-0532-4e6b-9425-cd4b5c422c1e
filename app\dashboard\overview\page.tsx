import { requireAuth } from '@/lib/auth'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/server'
import { RestaurantRequiredWrapper } from '@/components/restaurant/restaurant-required-wrapper'
import { cn } from '@/lib/utils'

export default async function DashboardOverviewPage() {
  // This will redirect to login if not authenticated
  const user = await requireAuth()

  // Fetch user's organization data
  const supabase = createClient()
  const { data: userData } = await supabase
    .from('users')
    .select(`
      organization_id,
      role,
      organizations (
        id,
        name,
        slug,
        subscription_status,
        trial_ends_at,
        created_at
      )
    `)
    .eq('id', user.id)
    .single()

  const organization = userData?.organizations
  const hasOrganization = !!organization
  const isOnTrial = organization?.subscription_status === 'trial'
  const trialEndsAt = organization?.trial_ends_at ? new Date(organization.trial_ends_at) : null
  const daysUntilTrialEnd = trialEndsAt ? Math.ceil((trialEndsAt.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 0

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-10">
          <header className="pb-6 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Dashboard Overview</h1>
                <p className="mt-2 text-gray-600">
                  Welcome back, {user.full_name || user.email}!
                  {organization && (
                    <span className="block text-sm text-orange-600 font-medium">
                      {organization.name}
                    </span>
                  )}
                </p>
              </div>
              <div className="flex gap-4">
                <Button variant="outline" asChild>
                  <Link href="/profile">Profile</Link>
                </Button>
                <form action="/auth/sign-out" method="GET">
                  <Button type="submit" variant="outline">
                    Sign Out
                  </Button>
                </form>
              </div>
            </div>
          </header>

          <main className="mt-8">
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
              {/* Quick Stats */}
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Restaurant
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {hasOrganization ? 'Active' : 'Not Set Up'}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 rounded-md flex items-center justify-center ${
                      isOnTrial ? 'bg-yellow-500' : hasOrganization ? 'bg-green-500' : 'bg-gray-500'
                    }`}>
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Subscription
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {isOnTrial ? 'Trial' : hasOrganization ? 'Active' : 'None'}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {isOnTrial ? 'Trial Days Left' : 'Member Since'}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {isOnTrial ? `${daysUntilTrialEnd} days` : new Date(user.created_at).toLocaleDateString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* No Restaurant Welcome State */}
            {!hasOrganization && (
              <div className="mt-8">
                <div className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-8 text-center">
                  <div className="mx-auto h-16 w-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mb-4">
                    <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Welcome to RestaurantSaaS!</h3>
                  <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                    You&apos;re all set up with your account! To access restaurant-specific features like menu management, 
                    team controls, and organization settings, you&apos;ll need to create your restaurant profile first.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button 
                      size="lg" 
                      className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700" 
                      asChild
                    >
                      <Link href="/onboarding/organization">
                        <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Create Your Restaurant
                      </Link>
                    </Button>
                    <Button variant="outline" size="lg" asChild>
                      <Link href="/demo">
                        <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Explore Demo
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Main Content Cards */}
            <div className={cn("mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2", !hasOrganization && "hidden")}>
              {/* Getting Started */}
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {hasOrganization ? 'Restaurant Status' : 'Getting Started'}
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-gray-900">Account created and verified</p>
                    </div>
                  </div>
                  
                  {hasOrganization ? (
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                          <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-gray-900">Restaurant set up successfully</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {organization.name} • {organization.slug}
                        </p>
                        {isOnTrial && (
                          <p className="text-xs text-yellow-600 mt-1">
                            Trial expires in {daysUntilTrialEnd} days
                          </p>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center">
                          <span className="text-xs font-medium text-orange-600">2</span>
                        </div>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-gray-900">Create your first restaurant</p>
                        <Button size="sm" className="mt-2 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700" asChild>
                          <Link href="/onboarding/organization">Set Up Restaurant</Link>
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Restaurant/User Profile */}
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {hasOrganization ? 'Restaurant Details' : 'Your Profile'}
                </h3>
                <div className="space-y-3">
                  {hasOrganization ? (
                    <>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Restaurant Name</dt>
                        <dd className="text-sm text-gray-900">{organization.name}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">URL Slug</dt>
                        <dd className="text-sm text-gray-900">{organization.slug}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Status</dt>
                        <dd className="text-sm text-gray-900 capitalize">{organization.subscription_status}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Created</dt>
                        <dd className="text-sm text-gray-900">{new Date(organization.created_at).toLocaleDateString()}</dd>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href="/settings/organization">Manage Restaurant</Link>
                      </Button>
                    </>
                  ) : (
                    <>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Email</dt>
                        <dd className="text-sm text-gray-900">{user.email}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Full Name</dt>
                        <dd className="text-sm text-gray-900">{user.full_name || 'Not set'}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Account Type</dt>
                        <dd className="text-sm text-gray-900">Restaurant Owner</dd>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href="/profile">Edit Profile</Link>
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="mt-8">
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {hasOrganization ? 'Quick Actions' : 'Recent Activity'}
                </h3>
                {hasOrganization ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <RestaurantRequiredWrapper blockInteraction={true}>
                      <Button variant="outline" className="h-20 flex flex-col items-center justify-center" asChild>
                        <Link href="/menu">
                          <svg className="h-6 w-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                          Manage Menu
                        </Link>
                      </Button>
                    </RestaurantRequiredWrapper>
                    <RestaurantRequiredWrapper blockInteraction={true}>
                      <Button variant="outline" className="h-20 flex flex-col items-center justify-center" asChild>
                        <Link href="/orders">
                          <svg className="h-6 w-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                          </svg>
                          View Orders
                        </Link>
                      </Button>
                    </RestaurantRequiredWrapper>
                    <RestaurantRequiredWrapper blockInteraction={true}>
                      <Button variant="outline" className="h-20 flex flex-col items-center justify-center" asChild>
                        <Link href="/dashboard/organization/multi-tenant">
                          <svg className="h-6 w-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                          </svg>
                          Multi-Tenant
                        </Link>
                      </Button>
                    </RestaurantRequiredWrapper>
                    <Button variant="outline" className="h-20 flex flex-col items-center justify-center" asChild>
                      <Link href="/settings">
                        <svg className="h-6 w-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Settings
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No activity yet</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Get started by creating your first restaurant.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
} 