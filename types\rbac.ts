export interface Permission {
  resource: string;
  action: string;
  scope?: string;
}

export interface Role {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  permissions: Permission[];
  isSystemRole: boolean;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserRole {
  id: string;
  userId: string;
  roleId: string;
  organizationId: string;
  assignedBy?: string;
  assignedAt: string;
  expiresAt?: string;
  isActive: boolean;
  role?: Role; // Joined data
}

export interface RoleAuditEntry {
  id: string;
  userId: string;
  targetUserId?: string;
  roleId?: string;
  organizationId: string;
  action: 'assigned' | 'removed' | 'modified' | 'created' | 'deleted';
  oldPermissions?: Permission[];
  newPermissions?: Permission[];
  metadata?: Record<string, any>;
  createdAt: string;
}

export interface TeamInvitation {
  id: string;
  organizationId: string;
  email: string;
  roleId?: string;
  invitedBy: string;
  expiresAt: string;
  acceptedAt?: string;
  status: 'pending' | 'accepted' | 'expired' | 'cancelled';
  token: string;
  createdAt: string;
  updatedAt: string;
  role?: Role; // Joined data
  inviter?: {
    id: string;
    fullName?: string;
    email: string;
  };
}

export interface TeamMember {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  role: 'owner' | 'admin' | 'manager' | 'staff';
  isActive: boolean;
  lastLoginAt?: string;
  invitedBy?: string;
  invitedAt?: string;
  acceptedAt?: string;
  createdAt: string;
  assignedRoles?: UserRole[];
  // Enhanced staff information
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  emergencyContact?: {
    name: string;
    phoneNumber: string;
    relationship: string;
  };
  salary?: {
    amount: number;
    currency: string;
    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly';
  };
  employmentType?: 'full-time' | 'part-time' | 'contract' | 'intern';
  department?: string;
  position?: string;
  startDate?: string;
  endDate?: string;
  notes?: string;
}

// Permission constants organized by resource
export const PERMISSIONS = {
  // Menu management
  MENU_CREATE: { resource: 'menu', action: 'create' },
  MENU_READ: { resource: 'menu', action: 'read' },
  MENU_UPDATE: { resource: 'menu', action: 'update' },
  MENU_DELETE: { resource: 'menu', action: 'delete' },
  MENU_PUBLISH: { resource: 'menu', action: 'publish' },
  MENU_MANAGE: { resource: 'menu', action: '*' },

  // User management
  USER_CREATE: { resource: 'user', action: 'create' },
  USER_READ: { resource: 'user', action: 'read' },
  USER_UPDATE: { resource: 'user', action: 'update' },
  USER_DELETE: { resource: 'user', action: 'delete' },
  USER_INVITE: { resource: 'user', action: 'invite' },
  USER_MANAGE: { resource: 'user', action: '*' },

  // Role management
  ROLE_CREATE: { resource: 'role', action: 'create' },
  ROLE_READ: { resource: 'role', action: 'read' },
  ROLE_UPDATE: { resource: 'role', action: 'update' },
  ROLE_DELETE: { resource: 'role', action: 'delete' },
  ROLE_ASSIGN: { resource: 'role', action: 'assign' },
  ROLE_MANAGE: { resource: 'role', action: '*' },

  // Organization management
  ORG_READ: { resource: 'organization', action: 'read' },
  ORG_UPDATE: { resource: 'organization', action: 'update' },
  ORG_DELETE: { resource: 'organization', action: 'delete' },
  ORG_BILLING: { resource: 'organization', action: 'billing' },
  ORG_SETTINGS: { resource: 'organization', action: 'settings' },
  ORG_MANAGE: { resource: 'organization', action: '*' },

  // Analytics
  ANALYTICS_READ: { resource: 'analytics', action: 'read' },
  ANALYTICS_EXPORT: { resource: 'analytics', action: 'export' },
  ANALYTICS_MANAGE: { resource: 'analytics', action: '*' },

  // Settings
  SETTINGS_READ: { resource: 'settings', action: 'read' },
  SETTINGS_UPDATE: { resource: 'settings', action: 'update' },
  SETTINGS_MANAGE: { resource: 'settings', action: '*' },
} as const;

// Default system roles
export const SYSTEM_ROLES = {
  OWNER: {
    name: 'Owner',
    description: 'Full access to all features and settings',
    permissions: [{ resource: '*', action: '*' }],
  },
  MANAGER: {
    name: 'Manager',
    description: 'Manage menus, view analytics, and manage team members',
    permissions: [
      PERMISSIONS.MENU_MANAGE,
      PERMISSIONS.ANALYTICS_READ,
      PERMISSIONS.USER_READ,
      PERMISSIONS.USER_INVITE,
      PERMISSIONS.ROLE_READ,
      PERMISSIONS.SETTINGS_READ,
    ],
  },
  STAFF: {
    name: 'Staff',
    description: 'Edit and manage menus',
    permissions: [
      PERMISSIONS.MENU_READ,
      PERMISSIONS.MENU_UPDATE,
      PERMISSIONS.MENU_CREATE,
    ],
  },
  VIEWER: {
    name: 'Viewer',
    description: 'View-only access to menus and basic analytics',
    permissions: [
      PERMISSIONS.MENU_READ,
      PERMISSIONS.ANALYTICS_READ,
    ],
  },
} as const;

// Form validation types
export interface CreateRoleData {
  name: string;
  description?: string;
  permissions: Permission[];
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  permissions?: Permission[];
}

export interface AssignRoleData {
  userId: string;
  roleId: string;
  expiresAt?: string;
}

export interface InviteTeamMemberData {
  email: string;
  roleId?: string;
  message?: string;
}

// Role hierarchy for permission checking
export const ROLE_HIERARCHY = {
  owner: 4,
  admin: 3,
  manager: 2,
  staff: 1,
  viewer: 0,
} as const;

export type RoleLevel = keyof typeof ROLE_HIERARCHY; 