import { createClient } from '@/lib/supabase/server'
import { createServiceClient } from '@/lib/supabase/server'
import { Database } from '@/types/database'
import { OrganizationFormData } from '@/lib/validations/organization'
import { validateSlugFormat } from '@/lib/utils/slug'

type Organization = Database['public']['Tables']['organizations']['Row']
type OrganizationInsert = Database['public']['Tables']['organizations']['Insert']
type UserUpdate = Database['public']['Tables']['users']['Update']

export interface CreateOrganizationResult {
  success: boolean
  organization?: Organization
  error?: string
}

export interface SlugAvailabilityResult {
  available: boolean
  suggestions?: string[]
}

/**
 * Check if a slug is available for use
 * @param slug - The slug to check
 * @returns Promise<boolean> - True if available, false if taken
 */
export async function isSlugAvailable(slug: string): Promise<boolean> {
  // First validate the slug format
  const validation = validateSlugFormat(slug)
  if (!validation.valid) {
    return false
  }

  try {
    const supabase = createClient()
    const { data, error } = await supabase
      .from('organizations')
      .select('id')
      .eq('slug', slug)
      .maybeSingle()

    if (error) {
      console.error('Error checking slug availability:', error)
      return false
    }

    // Slug is available if no organization was found
    return !data
  } catch (error) {
    console.error('Error checking slug availability:', error)
    return false
  }
}

/**
 * Create a new organization and assign the user as owner
 * @param data - Organization form data
 * @param userId - ID of the user creating the organization
 * @returns Promise<CreateOrganizationResult>
 */
export async function createOrganization(
  data: OrganizationFormData,
  userId: string
): Promise<CreateOrganizationResult> {
  try {
    const supabase = createServiceClient() // Use service role for admin operations

    // Check if slug is available
    const slugAvailable = await isSlugAvailable(data.slug)
    if (!slugAvailable) {
      return {
        success: false,
        error: 'This slug is already taken. Please choose a different one.'
      }
    }

    // Calculate trial end date (14 days from now)
    const trialEndsAt = new Date()
    trialEndsAt.setDate(trialEndsAt.getDate() + 14)

    // Prepare organization data
    const organizationData: OrganizationInsert = {
      name: data.name.trim(),
      slug: data.slug.trim(),
      description: data.description?.trim() || null,
      contact_email: data.contact_email?.trim() || null,
      phone: data.phone?.trim() || null,
      address: data.address?.trim() || null,
      city: data.city?.trim() || null,
      state: data.state?.trim() || null,
      country: data.country.trim(),
      postal_code: data.postal_code?.trim() || null,
      subscription_status: 'trial',
      trial_ends_at: trialEndsAt.toISOString(),
    }

    // Create the organization
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .insert(organizationData)
      .select()
      .single()

    if (orgError) {
      console.error('Error creating organization:', orgError)
      return {
        success: false,
        error: 'Failed to create organization. Please try again.'
      }
    }

    // Update user to link them to the organization and set as owner
    const userUpdate: UserUpdate = {
      organization_id: organization.id,
      role: 'owner',
      permissions: {
        manage_organization: true,
        manage_users: true,
        manage_menu: true,
        manage_billing: true,
      }
    }

    const { error: userError } = await supabase
      .from('users')
      .update(userUpdate)
      .eq('id', userId)

    if (userError) {
      console.error('Error updating user:', userError)
      // If user update fails, we should delete the organization to maintain consistency
      await supabase
        .from('organizations')
        .delete()
        .eq('id', organization.id)
      
      return {
        success: false,
        error: 'Failed to assign user to organization. Please try again.'
      }
    }

    // Initialize default menu structure
    try {
      await initializeDefaultMenu(organization.id)
    } catch (menuError) {
      console.error('Error initializing default menu:', menuError)
      // Don't fail the organization creation if menu initialization fails
      // The user can create menus later
    }

    // Initialize system roles and assign owner role
    try {
      const { initializeSystemRoles, assignRole } = await import('@/lib/db/rbac-queries')
      const systemRoles = await initializeSystemRoles(organization.id, userId)
      
      // Find the Owner role and assign it to the user
      const ownerRole = systemRoles.find(role => role.name === 'Owner')
      if (ownerRole) {
        await assignRole(userId, ownerRole.id, organization.id, userId)
        console.log(`Assigned Owner role to user ${userId} for organization ${organization.id}`)
      } else {
        console.error('Owner role not found after system role initialization')
      }
    } catch (roleError) {
      console.error('Error initializing system roles or assigning owner role:', roleError)
      // Don't fail the organization creation if role initialization fails
    }

    return {
      success: true,
      organization
    }

  } catch (error) {
    console.error('Error in createOrganization:', error)
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.'
    }
  }
}

/**
 * Initialize a default menu structure for a new organization
 * @param organizationId - ID of the organization
 */
async function initializeDefaultMenu(organizationId: string): Promise<void> {
  const supabase = createServiceClient()

  // Create a default menu
  const { data: menu, error: menuError } = await supabase
    .from('menus')
    .insert({
      organization_id: organizationId,
      name: 'Main Menu',
      description: 'Our main restaurant menu',
      is_active: true,
      is_published: false,
      display_order: 1,
      theme: {
        primaryColor: '#f97316', // Orange-500
        backgroundColor: '#ffffff',
        textColor: '#1f2937', // Gray-800
      },
      settings: {
        showPrices: true,
        showDescriptions: true,
        showImages: true,
        currency: 'USD',
      }
    })
    .select()
    .single()

  if (menuError) {
    throw new Error(`Failed to create default menu: ${menuError.message}`)
  }

  // Create default categories
  const defaultCategories = [
    { name: 'Appetizers', description: 'Start your meal with these delicious appetizers', display_order: 1 },
    { name: 'Main Courses', description: 'Our signature main dishes', display_order: 2 },
    { name: 'Desserts', description: 'Sweet treats to end your meal', display_order: 3 },
    { name: 'Beverages', description: 'Refreshing drinks and beverages', display_order: 4 },
  ]

  const { error: categoriesError } = await supabase
    .from('menu_categories')
    .insert(
      defaultCategories.map(category => ({
        menu_id: menu.id,
        ...category,
        is_active: true,
      }))
    )

  if (categoriesError) {
    throw new Error(`Failed to create default categories: ${categoriesError.message}`)
  }
}

/**
 * Get organization by ID
 * @param id - Organization ID
 * @returns Promise<Organization | null>
 */
export async function getOrganizationById(id: string): Promise<Organization | null> {
  try {
    const supabase = createClient()
    const { data, error } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching organization:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error fetching organization:', error)
    return null
  }
}

/**
 * Get organization by slug
 * @param slug - Organization slug
 * @returns Promise<Organization | null>
 */
export async function getOrganizationBySlug(slug: string): Promise<Organization | null> {
  try {
    const supabase = createClient()
    const { data, error } = await supabase
      .from('organizations')
      .select('*')
      .eq('slug', slug)
      .single()

    if (error) {
      console.error('Error fetching organization by slug:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error fetching organization by slug:', error)
    return null
  }
}

/**
 * Update organization
 * @param id - Organization ID
 * @param data - Update data
 * @returns Promise<Organization | null>
 */
export async function updateOrganization(
  id: string,
  data: Partial<OrganizationFormData>
): Promise<Organization | null> {
  try {
    const supabase = createServiceClient()
    
    // If slug is being updated, check availability
    if (data.slug) {
      const slugAvailable = await isSlugAvailable(data.slug)
      if (!slugAvailable) {
        throw new Error('This slug is already taken')
      }
    }

    const { data: organization, error } = await supabase
      .from('organizations')
      .update({
        ...data,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update organization: ${error.message}`)
    }

    return organization
  } catch (error) {
    console.error('Error updating organization:', error)
    return null
  }
}

/**
 * Check if user has permission to create an organization
 * @param userId - User ID
 * @returns Promise<boolean>
 */
export async function canUserCreateOrganization(userId: string): Promise<boolean> {
  try {
    const supabase = createClient()
    const { data: user, error } = await supabase
      .from('users')
      .select('organization_id')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error checking user organization status:', error)
      return false
    }

    // User can create an organization if they don't already belong to one
    return !user.organization_id
  } catch (error) {
    console.error('Error checking user organization status:', error)
    return false
  }
} 