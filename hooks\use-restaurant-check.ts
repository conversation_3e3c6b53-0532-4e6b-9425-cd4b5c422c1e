'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'

interface UseRestaurantCheckReturn {
  hasRestaurant: boolean | null
  loading: boolean
  checkAndRedirect: () => boolean
  restaurant: any | null
}

export function useRestaurantCheck(): UseRestaurantCheckReturn {
  const [hasRestaurant, setHasRestaurant] = useState<boolean | null>(null)
  const [restaurant, setRestaurant] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkRestaurantStatus()
  }, [])

  const checkRestaurantStatus = async () => {
    try {
      const supabase = createClient()
      
      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser()
      
      if (authError || !user) {
        setHasRestaurant(false)
        setLoading(false)
        return
      }

      // Check database for organization
      const { data: userData } = await supabase
        .from('users')
        .select(`
          organization_id,
          organizations (
            id,
            name,
            slug,
            subscription_status,
            description
          )
        `)
        .eq('id', user.id)
        .single()

      const hasOrg = !!(userData?.organizations && userData.organization_id)
      setHasRestaurant(hasOrg)
      setRestaurant(userData?.organizations || null)
    } catch (error) {
      console.error('Error checking restaurant status:', error)
      setHasRestaurant(false)
    } finally {
      setLoading(false)
    }
  }

  const checkAndRedirect = (): boolean => {
    if (hasRestaurant) {
      return true // User has restaurant, allow action
    }
    
    // User doesn't have restaurant, redirect to onboarding
    router.push('/onboarding/organization')
    return false // Block action
  }

  return {
    hasRestaurant,
    loading,
    checkAndRedirect,
    restaurant,
  }
} 