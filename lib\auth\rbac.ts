import { createClient } from '@/lib/supabase/server';
import { getCurrentUser } from '@/lib/auth';
import { Permission, ROLE_HIERARCHY, RoleLevel } from '@/types/roles';

export class PermissionError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'PermissionError';
  }
}

/**
 * Check if a user has a specific permission in an organization
 */
export async function hasPermission(
  userId: string, 
  organizationId: string, 
  permission: Permission
): Promise<boolean> {
  const supabase = createClient();
  
  try {
    // First check if user is owner of the organization (owners have all permissions)
    const { data: userCheck } = await supabase
      .from('users')
      .select('role, is_active')
      .eq('id', userId)
      .eq('organization_id', organizationId)
      .eq('is_active', true)
      .single();

    if (!userCheck) return false;

    // If user is owner, grant all permissions
    if (userCheck.role === 'owner') return true;

    // Use a direct query with proper join to users table instead of RPC for better type safety
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        roles!inner(permissions),
        users!inner(is_active)
      `)
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .eq('users.is_active', true);

    if (error) throw error;
    if (!data || data.length === 0) return false;

    // Check permissions from all user's roles
    for (const userRole of data) {
      const rolePermissions = userRole.roles.permissions;
      if (Array.isArray(rolePermissions)) {
        // Check for wildcard permissions (full access)
        const hasWildcard = rolePermissions.some((p: any) =>
          p.resource === '*' && p.action === '*'
        );
        if (hasWildcard) return true;

        const hasExactPermission = rolePermissions.some((p: any) =>
          p.resource === permission.resource &&
          p.action === permission.action &&
          (!permission.scope || p.scope === permission.scope)
        );

        if (hasExactPermission) return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
}

/**
 * Check if a user has any of the specified permissions
 */
export async function hasAnyPermission(
  userId: string,
  organizationId: string,
  permissions: Permission[]
): Promise<boolean> {
  for (const permission of permissions) {
    if (await hasPermission(userId, organizationId, permission)) {
      return true;
    }
  }
  return false;
}

/**
 * Check if a user has all of the specified permissions
 */
export async function hasAllPermissions(
  userId: string,
  organizationId: string,
  permissions: Permission[]
): Promise<boolean> {
  for (const permission of permissions) {
    if (!(await hasPermission(userId, organizationId, permission))) {
      return false;
    }
  }
  return true;
}

/**
 * Check if the current authenticated user has a specific permission
 */
export async function checkUserPermission(
  permission: Permission,
  organizationId?: string
): Promise<boolean> {
  try {
    const user = await getCurrentUser();
    if (!user) return false;

    // Get user's organization if not provided
    if (!organizationId) {
      const supabase = createClient();
      const { data: userProfile } = await supabase
        .from('users')
        .select('organization_id')
        .eq('id', user.id)
        .single();
      
      if (!userProfile?.organization_id) return false;
      organizationId = userProfile.organization_id;
    }

    return await hasPermission(user.id, organizationId, permission);
  } catch (error) {
    console.error('Error checking user permission:', error);
    return false;
  }
}

/**
 * Require that the current user has a specific permission, throw error if not
 */
export async function requirePermission(
  permission: Permission,
  organizationId?: string
): Promise<void> {
  const hasAccess = await checkUserPermission(permission, organizationId);
  if (!hasAccess) {
    throw new PermissionError(
      `Access denied. Required permission: ${permission.resource}.${permission.action}`,
      'INSUFFICIENT_PERMISSIONS'
    );
  }
}

/**
 * Get all effective permissions for a user in an organization
 */
export async function getUserEffectivePermissions(
  userId: string,
  organizationId: string
): Promise<Permission[]> {
  const supabase = createClient();
  
  try {
    // First check if user is owner (owners have all permissions)
    const { data: userCheck } = await supabase
      .from('users')
      .select('role, is_active')
      .eq('id', userId)
      .eq('organization_id', organizationId)
      .eq('is_active', true)
      .single();

    if (!userCheck) return [];

    // If user is owner, return all available permissions
    if (userCheck.role === 'owner') {
      // Import permissions here to avoid circular imports
      const { PERMISSIONS } = await import('@/types/roles');
      return Object.values(PERMISSIONS) as Permission[];
    }

    // Use a direct query with proper join to users table
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        roles!inner(permissions),
        users!inner(is_active)
      `)
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .eq('users.is_active', true);

    if (error) throw error;
    if (!data) return [];

    const allPermissions: Permission[] = [];
    
    // Collect permissions from all roles
    data.forEach(userRole => {
      const rolePermissions = userRole.roles.permissions;
      if (Array.isArray(rolePermissions)) {
        allPermissions.push(...(rolePermissions as unknown as Permission[]));
      }
    });

    // Remove duplicates
    const uniquePermissions = allPermissions.filter(
      (permission, index, self) =>
        index === self.findIndex(
          p => p.resource === permission.resource && 
               p.action === permission.action &&
               p.scope === permission.scope
        )
    );

    return uniquePermissions;
  } catch (error) {
    console.error('Error getting user permissions:', error);
    return [];
  }
}

/**
 * Check if a user has a higher role level than required
 */
export function hasRoleLevel(userLevel: RoleLevel, requiredLevel: RoleLevel): boolean {
  return ROLE_HIERARCHY[userLevel] >= ROLE_HIERARCHY[requiredLevel];
}

/**
 * Get the highest role level for a user
 */
export async function getUserRoleLevel(
  userId: string,
  organizationId: string
): Promise<RoleLevel | null> {
  const supabase = createClient();
  
  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        roles!inner(name)
      `)
      .eq('user_id', userId)
      .eq('organization_id', organizationId)


    if (error) throw error;
    if (!data || data.length === 0) return null;

    let highestLevel: RoleLevel | null = null;
    let highestLevelValue = -1;

    data.forEach(userRole => {
      const roleName = userRole.roles.name.toLowerCase() as RoleLevel;
      if (roleName in ROLE_HIERARCHY) {
        const levelValue = ROLE_HIERARCHY[roleName];
        if (levelValue > highestLevelValue) {
          highestLevel = roleName;
          highestLevelValue = levelValue;
        }
      }
    });

    return highestLevel;
  } catch (error) {
    console.error('Error getting user role level:', error);
    return null;
  }
}

/**
 * Enhanced permission checker that includes wildcard support
 */
export async function checkEnhancedPermission(
  userId: string,
  organizationId: string,
  resource: string,
  action: string,
  scope?: string
): Promise<boolean> {
  const supabase = createClient();
  
  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        roles!inner(permissions)
      `)
      .eq('user_id', userId)
      .eq('organization_id', organizationId)


    if (error) throw error;
    if (!data || data.length === 0) return false;

    // Check permissions from all user's roles
    for (const userRole of data) {
      const rolePermissions = userRole.roles.permissions;
      if (Array.isArray(rolePermissions)) {
        const hasPermission = rolePermissions.some((p: any) => {
          const resourceMatch = p.resource === '*' || p.resource === resource;
          const actionMatch = p.action === '*' || p.action === action;
          const scopeMatch = !scope || !p.scope || p.scope === scope;

          return resourceMatch && actionMatch && scopeMatch;
        });

        if (hasPermission) return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking enhanced permission:', error);
    return false;
  }
}

/**
 * Resource-based permission checker
 */
export async function canAccessResource(
  userId: string,
  organizationId: string,
  resource: string,
  action: 'create' | 'read' | 'update' | 'delete' = 'read'
): Promise<boolean> {
  return checkEnhancedPermission(userId, organizationId, resource, action);
}

/**
 * Middleware-friendly permission checker
 */
export function requirePermissions(permissions: Permission | Permission[]) {
  return async (userId: string, organizationId: string): Promise<boolean> => {
    const permissionsArray = Array.isArray(permissions) ? permissions : [permissions];
    return hasAnyPermission(userId, organizationId, permissionsArray);
  };
}

/**
 * Role-based access control for organization owners
 */
export async function isOrganizationOwner(
  userId: string,
  organizationId: string
): Promise<boolean> {
  const roleLevel = await getUserRoleLevel(userId, organizationId);
  return roleLevel === 'owner';
}

/**
 * Check if user is admin or higher
 */
export async function isAdminOrHigher(
  userId: string,
  organizationId: string
): Promise<boolean> {
  const roleLevel = await getUserRoleLevel(userId, organizationId);
  return roleLevel ? hasRoleLevel(roleLevel, 'admin') : false;
}

/**
 * Check if user can manage another user (based on role hierarchy)
 */
export async function canManageUser(
  managerId: string,
  targetUserId: string,
  organizationId: string
): Promise<boolean> {
  try {
    const supabase = createClient();
    
    const [managerData, targetData] = await Promise.all([
      supabase
        .from('users')
        .select('role')
        .eq('id', managerId)
        .eq('organization_id', organizationId)
        .single(),
      supabase
        .from('users')
        .select('role')
        .eq('id', targetUserId)
        .eq('organization_id', organizationId)
        .single()
    ]);

    if (!managerData.data || !targetData.data) return false;

    const managerLevel = ROLE_HIERARCHY[managerData.data.role as RoleLevel] || 0;
    const targetLevel = ROLE_HIERARCHY[targetData.data.role as RoleLevel] || 0;

    // Managers can manage users with lower or equal role level
    // Owners can manage everyone including other owners
    return managerLevel > targetLevel || 
           (managerData.data.role === 'owner' && targetData.data.role === 'owner');
  } catch (error) {
    console.error('Error checking user management permission:', error);
    return false;
  }
}

/**
 * Permission-based UI component wrapper
 */
export async function withPermission<T>(
  permission: Permission,
  component: T,
  fallback: T | null = null,
  organizationId?: string
): Promise<T | null> {
  const hasAccess = await checkUserPermission(permission, organizationId);
  return hasAccess ? component : fallback;
}

/**
 * Check multiple permissions and return which ones the user has
 */
export async function checkMultiplePermissions(
  userId: string,
  organizationId: string,
  permissions: Permission[]
): Promise<{ [key: string]: boolean }> {
  const results: { [key: string]: boolean } = {};
  
  await Promise.all(
    permissions.map(async (permission) => {
      const key = `${permission.resource}.${permission.action}`;
      results[key] = await hasPermission(userId, organizationId, permission);
    })
  );

  return results;
}

/**
 * Batch permission check for better performance
 */
export async function batchCheckPermissions(
  userId: string,
  organizationId: string,
  permissionKeys: string[]
): Promise<{ [key: string]: boolean }> {
  try {
    const results: { [key: string]: boolean } = {};

    // Check each permission using direct queries since RPC function may not exist
    await Promise.all(
      permissionKeys.map(async (permissionKey) => {
        try {
          // Parse permission key (format: resource.action)
          const [resource, action] = permissionKey.split('.');
          if (resource && action) {
            results[permissionKey] = await hasPermission(userId, organizationId, { resource, action });
          } else {
            results[permissionKey] = false;
          }
        } catch (error) {
          results[permissionKey] = false;
        }
      })
    );

    return results;
  } catch (error) {
    console.error('Error in batch permission check:', error);
    // Return all false if there's an error
    return permissionKeys.reduce((acc, key) => {
      acc[key] = false;
      return acc;
    }, {} as { [key: string]: boolean });
  }
}

// Export commonly used permission constants for convenience
export { ROLE_HIERARCHY } from '@/types/roles'; 