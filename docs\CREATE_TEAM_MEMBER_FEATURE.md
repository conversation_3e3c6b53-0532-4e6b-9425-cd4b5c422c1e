# Create Team Member Feature

## Overview

This feature allows owners and managers to create team member accounts directly instead of sending invitations. The created users receive temporary passwords and are required to set up their own secure passwords on first login.

## Key Features

### 1. Direct Account Creation
- **No email invitations required** - accounts are created immediately
- **Temporary password generation** - secure random passwords are generated
- **Immediate access** - users can login right away with temporary credentials
- **First-time password setup** - mandatory password change on first login

### 2. Enhanced User Information
- Basic information (email, full name, role)
- Contact details (phone, address, emergency contact)
- Employment information (position, department, salary, employment type)
- Additional notes and custom fields

### 3. Security Features
- **Temporary passwords** are randomly generated and shown only once
- **Password setup requirement** enforced through middleware
- **Role-based permissions** - only owners and managers can create accounts
- **Audit logging** - all account creation actions are logged

## User Flow

### For Administrators (Owners/Managers)

1. **Navigate to Team Management**
   - Go to `/dashboard/team`
   - Click "Create Team Member" button

2. **Fill Out Team Member Information**
   - **Basic Info**: Email (required), full name, role
   - **Contact**: Phone, address, emergency contact
   - **Employment**: Position, department, employment type, dates
   - **Additional**: Notes and custom information

3. **Account Creation**
   - Click "Create Team Member"
   - System generates temporary password
   - Account is created immediately
   - Temporary password is displayed (copy to clipboard)

4. **Share Credentials**
   - Securely communicate email and temporary password to new team member
   - Inform them they must change password on first login

### For New Team Members

1. **First Login**
   - Go to login page
   - Enter email and temporary password
   - System detects first-time login

2. **Password Setup**
   - Automatically redirected to `/setup-password`
   - Enter temporary password as "current password"
   - Choose new secure password
   - Confirm new password

3. **Access Dashboard**
   - After password setup, redirected to dashboard
   - Full access based on assigned role

## Technical Implementation

### API Endpoints

#### Create Team Member
```
POST /api/rbac/team/create
```

**Request Body:**
```json
{
  "organizationId": "uuid",
  "email": "<EMAIL>",
  "fullName": "John Doe",
  "roleId": "uuid",
  "phoneNumber": "+**********",
  "position": "Kitchen Staff",
  "department": "Kitchen",
  "employmentType": "full-time",
  "startDate": "2024-01-01",
  "salary": {
    "amount": 50000,
    "currency": "USD",
    "frequency": "yearly"
  }
}
```

**Response:**
```json
{
  "member": {
    "id": "uuid",
    "email": "<EMAIL>",
    "fullName": "John Doe",
    "organizationId": "uuid",
    "role": "staff",
    "isActive": true,
    "needsPasswordSetup": true,
    "temporaryPassword": "temp-password-123!",
    "createdBy": "creator-uuid",
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "message": "Team member created successfully"
}
```

### Database Changes

#### Users Table Extensions
- `needs_password_setup` metadata flag
- Enhanced staff information fields
- Employment and salary data

#### Authentication Flow
- Middleware checks for `needs_password_setup` flag
- Automatic redirection to password setup page
- Password setup completion updates user metadata

### Components

#### CreateTeamMemberForm
- **Location**: `components/team/create-team-member-form.tsx`
- **Features**: Multi-tab form with validation
- **Tabs**: Basic Info, Contact, Employment, Additional
- **Validation**: Zod schema validation with error handling

#### FirstTimePasswordSetup
- **Location**: `components/auth/first-time-password-setup.tsx`
- **Features**: Secure password setup with requirements
- **Validation**: Password strength requirements
- **Security**: Current password verification

### Permissions

#### Required Permissions
- `USER_CREATE` - Create team member accounts
- Available to: **Owner**, **Manager** roles

#### Permission Checks
- API endpoint validates permissions before creation
- UI components check permissions for button visibility
- Middleware enforces authentication requirements

## Security Considerations

### Password Security
- **Temporary passwords** are cryptographically random
- **One-time display** - passwords shown only once during creation
- **Mandatory change** - users cannot access system without password setup
- **Strength requirements** - new passwords must meet security criteria

### Access Control
- **Role-based creation** - only authorized users can create accounts
- **Organization isolation** - users can only create accounts in their organization
- **Audit trail** - all creation actions are logged with metadata

### Data Protection
- **Sensitive data handling** - salary and personal information properly secured
- **RLS policies** - database-level security for multi-tenant isolation
- **Metadata encryption** - sensitive fields can be encrypted at rest

## Usage Examples

### Basic Team Member Creation
```typescript
const teamMemberData = {
  email: '<EMAIL>',
  fullName: 'John Doe',
  roleId: 'staff-role-uuid'
};

const response = await fetch('/api/rbac/team/create', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    organizationId: 'org-uuid',
    ...teamMemberData
  })
});
```

### Enhanced Team Member Creation
```typescript
const enhancedData = {
  email: '<EMAIL>',
  fullName: 'Jane Smith',
  roleId: 'manager-role-uuid',
  phoneNumber: '******-0123',
  position: 'Kitchen Manager',
  department: 'Kitchen',
  employmentType: 'full-time',
  startDate: '2024-01-15',
  salary: {
    amount: 65000,
    currency: 'USD',
    frequency: 'yearly'
  },
  emergencyContact: {
    name: 'John Smith',
    phoneNumber: '******-0124',
    relationship: 'Spouse'
  }
};
```

## Testing

### Test Page
- **Location**: `/test-create-member`
- **Purpose**: Manual testing of account creation
- **Features**: Simple form to test API endpoint

### Test Scenarios
1. **Basic account creation** with minimal information
2. **Enhanced account creation** with full information
3. **Permission validation** - unauthorized access attempts
4. **Duplicate email handling** - error handling for existing emails
5. **First-time login flow** - password setup process

## Migration from Invitation System

### Comparison

| Feature | Invitation System | Create Team Member |
|---------|------------------|-------------------|
| **Setup Time** | Email + acceptance | Immediate |
| **User Experience** | Email verification required | Direct login |
| **Password Control** | User chooses initially | Admin provides temporary |
| **Security** | Email-based verification | Temporary password + mandatory change |
| **Offline Setup** | Requires email access | Can be done offline |

### When to Use Each

#### Use Create Team Member When:
- Setting up accounts for staff without email access
- Bulk account creation for new restaurant openings
- Training environments or temporary accounts
- Immediate access required

#### Use Invitation System When:
- Remote team members
- External consultants or contractors
- Users prefer to set their own initial passwords
- Email verification is required by policy

## Future Enhancements

### Planned Features
1. **Bulk creation** - CSV import for multiple team members
2. **QR code sharing** - Generate QR codes for credential sharing
3. **SMS notifications** - Send temporary passwords via SMS
4. **Password policies** - Configurable password requirements
5. **Account templates** - Predefined role and information templates

### Integration Opportunities
1. **HR systems** - Import from existing HR databases
2. **Time tracking** - Integration with scheduling systems
3. **Payroll systems** - Automatic salary information sync
4. **Training platforms** - Account creation triggers training enrollment

## Troubleshooting

### Common Issues

#### "User with this email already exists"
- **Cause**: Email already registered in Supabase Auth
- **Solution**: Use different email or check existing accounts

#### "Permission denied"
- **Cause**: User lacks USER_CREATE permission
- **Solution**: Verify user role and permissions

#### "Failed to create user account"
- **Cause**: Supabase Auth service error
- **Solution**: Check Supabase service status and configuration

#### "Password setup not working"
- **Cause**: User metadata not properly set
- **Solution**: Verify `needs_password_setup` flag in user metadata

### Debug Steps
1. Check browser console for JavaScript errors
2. Verify API endpoint responses in Network tab
3. Check Supabase Auth logs for account creation
4. Verify database user record creation
5. Test password setup flow with temporary credentials

## Support

For issues or questions about the Create Team Member feature:

1. **Check this documentation** for common scenarios
2. **Review test page** at `/test-create-member` for examples
3. **Check API logs** for detailed error information
4. **Verify permissions** for the creating user
5. **Test password setup flow** with generated credentials 