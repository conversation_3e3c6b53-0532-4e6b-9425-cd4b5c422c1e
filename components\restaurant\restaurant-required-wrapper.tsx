'use client'

import { ReactNode } from 'react'
import { useRestaurantCheck } from '@/hooks/use-restaurant-check'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Store, Plus } from 'lucide-react'
import Link from 'next/link'

interface RestaurantRequiredWrapperProps {
  children: ReactNode
  fallback?: ReactNode
  showAlert?: boolean
  blockInteraction?: boolean
  className?: string
}

export function RestaurantRequiredWrapper({
  children,
  fallback,
  showAlert = false,
  blockInteraction = true,
  className = '',
}: RestaurantRequiredWrapperProps) {
  const { hasRestaurant, loading, checkAndRedirect } = useRestaurantCheck()

  // Show loading state
  if (loading) {
    return <div className={`animate-pulse bg-gray-200 rounded ${className}`}>Loading...</div>
  }

  // If user has restaurant, render children normally
  if (hasRestaurant) {
    return <>{children}</>
  }

  // If custom fallback is provided, show it
  if (fallback) {
    return <>{fallback}</>
  }

  // If showAlert is true, show a helpful alert
  if (showAlert) {
    return (
      <Alert className={className}>
        <Store className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>You need to set up your restaurant first to access this feature.</span>
          <Button asChild size="sm" className="ml-4">
            <Link href="/onboarding/organization">
              <Plus className="h-4 w-4 mr-1" />
              Add Restaurant
            </Link>
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  // If blockInteraction is true, wrap children in a click handler that redirects
  if (blockInteraction) {
    return (
      <div
        className={`cursor-pointer ${className}`}
        onClick={(e) => {
          e.preventDefault()
          e.stopPropagation()
          checkAndRedirect()
        }}
      >
        {children}
      </div>
    )
  }

  // Default: just render children (they'll work but might show empty states)
  return <>{children}</>
} 