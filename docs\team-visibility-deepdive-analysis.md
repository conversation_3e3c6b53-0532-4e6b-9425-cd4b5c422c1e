# Team Visibility Deep Dive Analysis - RS-006

## Executive Summary

**Issue**: Team page shows only 1 employee instead of 2 total employees in the organization.

**Root Cause**: Row Level Security (RLS) policies on the `users` table are too restrictive, only allowing users to see their own profile (`auth.uid() = id`) instead of allowing organization members to see each other.

**Status**: Issue identified but requires careful solution to avoid breaking authentication system.

## Current Database State

### Users Table Data
```sql
-- 5 total users across different organizations
SELECT id, email, full_name, organization_id, role, is_active FROM users;

-- Organization "4c842442-af6b-4dbb-8415-c9edb0e5eb0a" (daniel-test) has 2 users:
-- 1. <EMAIL> (faneil) - role: staff
-- 2. <EMAIL> (daniel) - role: owner
```

### Current RLS Policies
```sql
-- RESTRICTIVE POLICIES (Current State)
"Users can view own profile" - FOR SELECT USING (auth.uid() = id)
"Users can insert own profile" - FOR INSERT WITH CHECK (auth.uid() = id)  
"Users can update own profile" - FOR UPDATE USING (auth.uid() = id)
```

## Problem Analysis

### 1. API Flow Analysis
```typescript
// app/dashboard/team/page.tsx (Line 60-67)
const [membersRes, invitationsRes] = await Promise.all([
  fetch(`/api/rbac/team?organizationId=${orgId}&type=members`),
  fetch(`/api/rbac/team?organizationId=${orgId}&type=invitations`)
]);

// app/api/rbac/team/route.ts (Line 31)
const members = await getTeamMembers(organizationId);

// lib/db/rbac-queries.ts (Line 355-360)
const { data, error } = await supabase
  .from('users')
  .select('*')
  .eq('organization_id', organizationId)
  .eq('is_active', true)
  .order('created_at', { ascending: true });
```

### 2. RLS Policy Impact
- Query filters by `organization_id` and `is_active = true` ✅
- But RLS policy only allows `auth.uid() = id` ❌
- Result: Only the current user's record is returned
- Expected: All users in the same organization should be visible

### 3. Authentication Context
- API uses `createClient()` which includes user authentication context
- RLS policies are enforced at database level
- Current user can only see their own record due to restrictive policy

## Intended vs Current Behavior

### Intended RLS Policies (from lib/security/rls-policies.sql)
```sql
-- ORGANIZATION-BASED POLICIES (Intended)
CREATE POLICY "Users can see organization members" ON users
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() AND is_active = true
    )
  );
```

### Current Behavior
- User can only see themselves in team list
- Team page shows 1/2 members

### Expected Behavior  
- User can see all members in their organization
- Team page shows 2/2 members

## Risk Assessment

### High Risk Areas
1. **Authentication System**: Changing RLS policies affects core security
2. **Data Access**: Overly permissive policies could expose sensitive data
3. **Multi-tenancy**: Organization isolation must be maintained

### Safe Approach Required
- Test changes in development environment first
- Gradual policy updates with rollback capability
- Verify organization isolation remains intact

## Solution Options

### Option 1: Service Role Query (RECOMMENDED)
**Approach**: Use service role for team queries to bypass RLS
**Pros**: No RLS changes, maintains security, immediate fix
**Cons**: Requires careful implementation to maintain organization filtering

### Option 2: Gradual RLS Policy Update
**Approach**: Update policies with extensive testing
**Pros**: Proper long-term solution, follows intended design
**Cons**: Higher risk, requires careful rollout

### Option 3: API-Level Organization Filtering
**Approach**: Enhanced server-side filtering with service client
**Pros**: Maintains RLS security, adds extra validation layer
**Cons**: More complex implementation

## Recommended Implementation

### Phase 1: Immediate Fix (Service Role)
```typescript
// lib/db/rbac-queries.ts
export async function getTeamMembers(organizationId: string): Promise<TeamMember[]> {
  const supabase = createServiceClient(); // Use service role
  
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('organization_id', organizationId)
    .eq('is_active', true)
    .order('created_at', { ascending: true });
    
  // Additional validation: Ensure requester belongs to organization
  // This maintains security while allowing team visibility
}
```

### Phase 2: Long-term RLS Fix (Future)
- Comprehensive testing environment setup
- Gradual policy migration with rollback plan
- Full security audit after changes

## Testing Strategy

### Before Any Changes
1. Document current authentication flows
2. Test user isolation between organizations
3. Verify all existing functionality

### After Implementation
1. Verify team page shows all organization members
2. Confirm users cannot see other organizations' data
3. Test all authentication-dependent features
4. Performance impact assessment

## Security Considerations

### Must Maintain
- Organization data isolation
- User profile privacy settings
- Role-based access controls
- Authentication integrity

### Additional Safeguards
- Server-side organization membership validation
- Audit logging for team data access
- Rate limiting on team queries
- Input validation on organization IDs

## Next Steps

1. **Immediate**: Implement service role solution for team queries
2. **Short-term**: Add comprehensive logging and monitoring
3. **Long-term**: Plan proper RLS policy migration strategy
4. **Ongoing**: Regular security audits and testing

## Files Requiring Changes

### Primary Files
- `lib/db/rbac-queries.ts` - Update getTeamMembers function
- `lib/supabase/service-client.ts` - Ensure service client available

### Secondary Files (Monitoring)
- Add logging to team API endpoints
- Update error handling for organization validation

## Conclusion

The team visibility issue is caused by overly restrictive RLS policies. The safest immediate solution is to use service role queries with proper organization filtering, while planning a careful migration to the intended RLS policies for the long term.

**Priority**: High - Affects core team management functionality
**Complexity**: Medium - Requires careful security considerations
**Risk**: Medium - Must maintain organization isolation
