import { FirstTimePasswordSetup } from "@/components/auth/first-time-password-setup"
import { createClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"

export default async function SetupPasswordPage() {
  try {
    const supabase = createClient()
    const { data: { user }, error } = await supabase.auth.getUser()
    
    console.log('Setup password page - auth user:', user)
    console.log('Setup password page - user metadata:', user?.user_metadata)
    
    if (error || !user) {
      console.log('No user found, redirecting to login')
      redirect('/login')
    }

    // Check if user needs password setup from user_metadata
    const needsPasswordSetup = user.user_metadata?.needs_password_setup === true
    
    console.log('Needs password setup:', needsPasswordSetup)
    
    if (!needsPasswordSetup) {
      console.log('User does not need password setup, redirecting to dashboard')
      redirect('/dashboard/overview')
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="w-full max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900">Complete Your Setup</h1>
            <p className="text-gray-600 mt-2">Set up your secure password to access your account</p>
          </div>
          <FirstTimePasswordSetup userEmail={user.email || ''} />
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error in setup password page:', error)
    
    // Return a fallback component for debugging
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
          <h1 className="text-xl font-bold text-red-600 mb-4">Setup Password Error</h1>
          <p className="text-gray-600 mb-4">There was an error loading the password setup page.</p>
          <p className="text-sm text-gray-500">Error: {error instanceof Error ? error.message : 'Unknown error'}</p>
          <a href="/login" className="mt-4 inline-block bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">
            Back to Login
          </a>
        </div>
      </div>
    )
  }
} 