"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";

import { 
  organizationSchema, 
  OrganizationFormData, 
  RESTAURANT_TYPES 
} from "@/lib/validations/organization";
import { generateSlug } from "@/lib/utils/slug";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Loader2, Check, X, AlertCircle, Building, Mail } from "lucide-react";

interface SlugCheckResult {
  available: boolean;
  error?: string;
  suggestions?: string[];
}

export function OrganizationForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [slugLoading, setSlugLoading] = useState(false);
  const [slugCheck, setSlugCheck] = useState<SlugCheckResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: "",
      slug: "",
      description: "",
      restaurant_type: undefined,
      contact_email: "",
      phone: "",
      address: "",
      city: "",
      state: "",
      country: "United States",
      postal_code: "",
    },
  });

  const { watch, setValue } = form;
  const nameValue = watch("name");
  const slugValue = watch("slug");

  // Auto-generate slug from restaurant name
  useEffect(() => {
    if (nameValue && nameValue.length > 0) {
      const autoSlug = generateSlug(nameValue);
      if (autoSlug !== slugValue) {
        setValue("slug", autoSlug);
        setSlugCheck(null);
      }
    }
  }, [nameValue, slugValue, setValue]);

  // Check slug availability
  const checkSlugAvailability = async (slug: string) => {
    if (!slug || slug.length < 3) {
      setSlugCheck(null);
      return;
    }

    setSlugLoading(true);
    try {
      const response = await fetch("/api/organizations/check-slug", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ slug }),
      });

      if (!response.ok) {
        throw new Error("Failed to check slug availability");
      }

      const result: SlugCheckResult = await response.json();
      setSlugCheck(result);
    } catch (error) {
      console.error("Error checking slug:", error);
      setSlugCheck({ available: false, error: "Failed to check availability" });
    } finally {
      setSlugLoading(false);
    }
  };

  // Debounced slug check
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (slugValue) {
        checkSlugAvailability(slugValue);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [slugValue]);

  const onSubmit = async (data: OrganizationFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!slugCheck?.available) {
        setError("Please choose an available slug");
        setIsLoading(false);
        return;
      }

      const response = await fetch("/api/organizations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create organization");
      }

      // Redirect to dashboard - middleware will handle further navigation
      router.push("/dashboard/overview");
    } catch (error) {
      console.error("Error creating organization:", error);
      setError(error instanceof Error ? error.message : "Failed to create organization");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSlugSuggestion = (suggestion: string) => {
    setValue("slug", suggestion);
    setSlugCheck(null);
  };

  const getSlugStatusIcon = () => {
    if (slugLoading) {
      return <Loader2 className="h-4 w-4 animate-spin text-gray-400" />;
    }
    if (slugCheck?.available === true) {
      return <Check className="h-4 w-4 text-green-500" />;
    }
    if (slugCheck?.available === false) {
      return <X className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl w-full space-y-8">
        <div className="text-center">
          <Building className="mx-auto h-12 w-12 text-orange-600" />
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Set up your restaurant
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Let&apos;s create your restaurant profile to get started
          </p>
        </div>

        <Card className="p-8">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {error && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="flex">
                    <AlertCircle className="h-5 w-5 text-red-400" />
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Error creating organization
                      </h3>
                      <p className="mt-1 text-sm text-red-700">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <Building className="h-5 w-5 mr-2 text-orange-600" />
                  Restaurant Information
                </h3>

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Restaurant Name *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your restaurant name"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>URL Slug *</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            placeholder="your-restaurant-name"
                            {...field}
                            disabled={isLoading}
                            className="pr-10"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                            {getSlugStatusIcon()}
                          </div>
                        </div>
                      </FormControl>
                      <FormDescription>
                        Your restaurant&apos;s web address: yourdomain.com/{slugValue || "your-slug"}
                      </FormDescription>
                      {slugCheck && !slugCheck.available && (
                        <div className="mt-2">
                          <p className="text-sm text-red-600">
                            {slugCheck.error || "This slug is already taken"}
                          </p>
                          {slugCheck.suggestions && slugCheck.suggestions.length > 0 && (
                            <div className="mt-2">
                              <p className="text-sm text-gray-600">Try these alternatives:</p>
                              <div className="flex flex-wrap gap-2 mt-1">
                                {slugCheck.suggestions.map((suggestion) => (
                                  <Button
                                    key={suggestion}
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleSlugSuggestion(suggestion)}
                                    disabled={isLoading}
                                  >
                                    {suggestion}
                                  </Button>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="restaurant_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Restaurant Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={isLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select restaurant type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {RESTAURANT_TYPES.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <textarea
                          className="flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          placeholder="Tell customers about your restaurant..."
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        Brief description of your restaurant (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <Mail className="h-5 w-5 mr-2 text-orange-600" />
                  Contact Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="contact_email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contact Email</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder="+****************"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex justify-center pt-6">
                <Button
                  type="submit"
                  className="w-full sm:w-auto px-8 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                  disabled={isLoading || !slugCheck?.available}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating restaurant...
                    </>
                  ) : (
                    "Create Restaurant"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </Card>
      </div>
    </div>
  );
} 