'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Zap, Shield, Users, Eye, Settings, ChefHat, Utensils, Calendar } from 'lucide-react';
import { Permission, PERMISSIONS } from '@/types/roles';

interface QuickRoleCreatorProps {
  organizationId: string;
  onSuccess: () => void;
  onCancel: () => void;
}

interface RoleTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  permissions: Permission[];
  color: string;
}

export default function QuickRoleCreator({ organizationId, onSuccess, onCancel }: QuickRoleCreatorProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [customName, setCustomName] = useState('');
  const [customDescription, setCustomDescription] = useState('');

  const roleTemplates: RoleTemplate[] = [
    {
      id: 'kitchen-staff',
      name: 'Kitchen Staff',
      description: 'Full access to menu management and basic restaurant operations',
      icon: <ChefHat className="h-6 w-6" />,
      permissions: [
        PERMISSIONS.MENU_READ,
        PERMISSIONS.MENU_CREATE,
        PERMISSIONS.MENU_UPDATE,
        PERMISSIONS.SETTINGS_VIEW,
      ],
      color: 'bg-orange-100 text-orange-800 border-orange-200',
    },
    {
      id: 'server',
      name: 'Server/Wait Staff',
      description: 'View menus and manage basic customer service tasks',
      icon: <Utensils className="h-6 w-6" />,
      permissions: [
        PERMISSIONS.MENU_READ,
        PERMISSIONS.USER_VIEW,
        PERMISSIONS.SETTINGS_VIEW,
      ],
      color: 'bg-blue-100 text-blue-800 border-blue-200',
    },
    {
      id: 'manager',
      name: 'Floor Manager',
      description: 'Manage staff, oversee operations, and access analytics',
      icon: <Users className="h-6 w-6" />,
      permissions: [
        PERMISSIONS.MENU_MANAGE,
        PERMISSIONS.USER_VIEW,
        PERMISSIONS.USER_INVITE,
        PERMISSIONS.ANALYTICS_READ,
        PERMISSIONS.SETTINGS_VIEW,
      ],
      color: 'bg-purple-100 text-purple-800 border-purple-200',
    },
    {
      id: 'viewer',
      name: 'Viewer/Observer',
      description: 'Read-only access for viewing data and reports',
      icon: <Eye className="h-6 w-6" />,
      permissions: [
        PERMISSIONS.MENU_READ,
        PERMISSIONS.ANALYTICS_READ,
        PERMISSIONS.SETTINGS_VIEW,
      ],
      color: 'bg-gray-100 text-gray-800 border-gray-200',
    },
    {
      id: 'event-coordinator',
      name: 'Event Coordinator',
      description: 'Manage events, special menus, and customer relations',
      icon: <Calendar className="h-6 w-6" />,
      permissions: [
        PERMISSIONS.MENU_READ,
        PERMISSIONS.MENU_CREATE,
        PERMISSIONS.MENU_UPDATE,
        PERMISSIONS.USER_VIEW,
        PERMISSIONS.ANALYTICS_READ,
        PERMISSIONS.SETTINGS_VIEW,
      ],
      color: 'bg-green-100 text-green-800 border-green-200',
    },
    {
      id: 'admin',
      name: 'Administrator',
      description: 'Full access to most features except organization settings',
      icon: <Settings className="h-6 w-6" />,
      permissions: [
        PERMISSIONS.MENU_MANAGE,
        PERMISSIONS.USER_MANAGE,
        PERMISSIONS.ROLE_READ,
        PERMISSIONS.ANALYTICS_MANAGE,
        PERMISSIONS.SETTINGS_VIEW,
      ],
      color: 'bg-red-100 text-red-800 border-red-200',
    },
  ];

  const getSelectedTemplate = () => {
    return roleTemplates.find(t => t.id === selectedTemplate);
  };

  const handleCreateRole = async () => {
    const template = getSelectedTemplate();
    if (!template) {
      toast({
        title: 'Selection Required',
        description: 'Please select a role template to continue',
        variant: 'destructive',
      });
      return;
    }

    if (!customName.trim()) {
      toast({
        title: 'Name Required',
        description: 'Please provide a name for the role',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);

    try {
      const roleData = {
        organizationId,
        name: customName.trim(),
        description: customDescription.trim() || template.description,
        permissions: template.permissions,
      };

      const response = await fetch('/api/rbac/roles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(roleData),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: `Role "${customName}" created successfully`,
        });
        onSuccess();
      } else {
        const error = await response.json();
        toast({
          title: 'Error',
          description: error.error || 'Failed to create role',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating role:', error);
      toast({
        title: 'Error',
        description: 'Failed to create role',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Quick Role Creator
        </CardTitle>
        <CardDescription>
          Create a new role in just a few clicks using our pre-configured templates
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Step 1: Select Template */}
        <div>
          <h3 className="text-lg font-semibold mb-4">1. Choose a Role Template</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {roleTemplates.map((template) => (
              <Card
                key={template.id}
                className={`cursor-pointer transition-all border-2 ${
                  selectedTemplate === template.id
                    ? template.color.replace('100', '200') + ' border-current'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedTemplate(template.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`p-2 rounded-lg ${template.color}`}>
                      {template.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-sm">{template.name}</h4>
                    </div>
                    {selectedTemplate === template.id && (
                      <Shield className="h-5 w-5 text-green-600" />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                  <div className="flex flex-wrap gap-1">
                    {template.permissions.slice(0, 3).map((permission, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {permission.resource}.{permission.action}
                      </Badge>
                    ))}
                    {template.permissions.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{template.permissions.length - 3} more
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Step 2: Customize Role */}
        {selectedTemplate && (
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold mb-4">2. Customize Role Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="roleName" className="text-sm font-medium">
                  Role Name *
                </Label>
                <Input
                  id="roleName"
                  placeholder={getSelectedTemplate()?.name || 'Enter role name'}
                  value={customName}
                  onChange={(e) => setCustomName(e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="roleDescription" className="text-sm font-medium">
                  Description
                </Label>
                <Textarea
                  id="roleDescription"
                  placeholder={getSelectedTemplate()?.description || 'Enter role description'}
                  value={customDescription}
                  onChange={(e) => setCustomDescription(e.target.value)}
                  rows={3}
                  className="mt-1"
                />
              </div>
            </div>

            {/* Permission Preview */}
            <div className="mt-4">
              <Label className="text-sm font-medium mb-2 block">Included Permissions:</Label>
              <div className="flex flex-wrap gap-2">
                {getSelectedTemplate()?.permissions.map((permission, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {permission.resource}.{permission.action}
                  </Badge>
                ))}
              </div>
              <p className="text-sm text-gray-500 mt-2">
                You can modify these permissions later in the role management section.
              </p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          
          <Button 
            onClick={handleCreateRole}
            disabled={loading || !selectedTemplate || !customName.trim()}
            className="bg-orange-600 hover:bg-orange-700"
          >
            {loading ? 'Creating...' : 'Create Role'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 