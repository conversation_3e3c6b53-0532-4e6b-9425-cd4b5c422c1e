import { z } from 'zod';

// Permission validation schema
export const permissionSchema = z.object({
  resource: z.string().min(1, 'Resource is required'),
  action: z.string().min(1, 'Action is required'),
  scope: z.enum(['own', 'team', 'organization']).optional(),
});

// Role schemas
export const createRoleSchema = z.object({
  name: z
    .string()
    .min(1, 'Role name is required')
    .max(50, 'Role name must be 50 characters or less')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Role name can only contain letters, numbers, spaces, hyphens, and underscores'),
  description: z
    .string()
    .max(500, 'Description must be 500 characters or less')
    .optional(),
  permissions: z
    .array(permissionSchema)
    .min(1, 'At least one permission is required'),
});

export const updateRoleSchema = createRoleSchema.partial();

// User role assignment schemas
export const assignRoleSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  roleId: z.string().uuid('Invalid role ID'),
  expiresAt: z.string().datetime().optional(),
});

export const removeRoleSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  roleId: z.string().uuid('Invalid role ID'),
});

export const bulkAssignRolesSchema = z.object({
  userIds: z.array(z.string().uuid()).min(1, 'At least one user ID is required'),
  roleId: z.string().uuid('Invalid role ID'),
  expiresAt: z.string().datetime().optional(),
});

// Team invitation schemas
export const inviteTeamMemberSchema = z.object({
  email: z.string().email('Invalid email address'),
  roleId: z.string().uuid('Invalid role ID').optional(),
  message: z.string().max(1000, 'Message must be 1000 characters or less').optional(),
});

// Team member creation schemas
export const createTeamMemberSchema = z.object({
  email: z.string().email('Invalid email address'),
  fullName: z.string().min(1, 'Full name is required').max(100, 'Full name must not exceed 100 characters').optional(),
  roleId: z.string().uuid('Invalid role ID').optional(),
  
  // Contact information
  phoneNumber: z.string().max(20, 'Phone number must not exceed 20 characters').optional(),
  dateOfBirth: z.string().optional(),
  address: z.string().max(200, 'Address must not exceed 200 characters').optional(),
  city: z.string().max(100, 'City must not exceed 100 characters').optional(),
  state: z.string().max(100, 'State must not exceed 100 characters').optional(),
  postalCode: z.string().max(20, 'Postal code must not exceed 20 characters').optional(),
  country: z.string().max(100, 'Country must not exceed 100 characters').optional(),
  
  // Emergency contact
  emergencyContact: z.object({
    name: z.string().min(1, 'Emergency contact name is required'),
    phoneNumber: z.string().min(1, 'Emergency contact phone is required'),
    relationship: z.string().min(1, 'Relationship is required'),
  }).optional(),
  
  // Employment information
  position: z.string().max(100, 'Position must not exceed 100 characters').optional(),
  department: z.string().max(100, 'Department must not exceed 100 characters').optional(),
  employmentType: z.enum(['full-time', 'part-time', 'contract', 'intern']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  
  // Salary information
  salary: z.object({
    amount: z.number().min(0, 'Salary amount must be positive'),
    currency: z.string().length(3, 'Currency must be 3 characters'),
    frequency: z.enum(['hourly', 'daily', 'weekly', 'monthly', 'yearly']),
  }).optional(),
  
  // Additional information
  notes: z.string().max(1000, 'Notes must not exceed 1000 characters').optional(),
});

// Enhanced team invitation schema with staff details
export const enhancedInviteTeamMemberSchema = z.object({
  email: z.string().email('Invalid email address'),
  fullName: z.string().max(255, 'Name must be 255 characters or less').optional(),
  roleId: z.string().uuid('Invalid role ID').optional(),
  message: z.string().max(1000, 'Message must be 1000 characters or less').optional(),
  
  // Contact information
  phoneNumber: z.string().max(20, 'Phone number must be 20 characters or less').optional(),
  dateOfBirth: z.string().date('Invalid date format').optional(),
  address: z.string().max(500, 'Address must be 500 characters or less').optional(),
  city: z.string().max(100, 'City must be 100 characters or less').optional(),
  state: z.string().max(100, 'State must be 100 characters or less').optional(),
  postalCode: z.string().max(20, 'Postal code must be 20 characters or less').optional(),
  country: z.string().max(100, 'Country must be 100 characters or less').optional(),
  
  // Emergency contact
  emergencyContact: z.object({
    name: z.string().max(255, 'Emergency contact name must be 255 characters or less'),
    phoneNumber: z.string().max(20, 'Emergency contact phone must be 20 characters or less'),
    relationship: z.string().max(100, 'Relationship must be 100 characters or less'),
  }).optional(),
  
  // Employment information
  position: z.string().max(100, 'Position must be 100 characters or less').optional(),
  department: z.string().max(100, 'Department must be 100 characters or less').optional(),
  employmentType: z.enum(['full-time', 'part-time', 'contract', 'intern']).optional(),
  startDate: z.string().date('Invalid start date format').optional(),
  endDate: z.string().date('Invalid end date format').optional(),
  
  // Salary information
  salary: z.object({
    amount: z.number().positive('Salary amount must be positive'),
    currency: z.string().max(10, 'Currency must be 10 characters or less'),
    frequency: z.enum(['hourly', 'daily', 'weekly', 'monthly', 'yearly']),
  }).optional(),
  
  // Additional information
  notes: z.string().max(2000, 'Notes must be 2000 characters or less').optional(),
});

export const acceptInvitationSchema = z.object({
  token: z.string().min(1, 'Invitation token is required'),
  fullName: z.string().min(1, 'Full name is required').optional(),
});

export const cancelInvitationSchema = z.object({
  invitationId: z.string().uuid('Invalid invitation ID'),
});

// Permission checking schemas
export const checkPermissionSchema = z.object({
  permission: permissionSchema,
  userId: z.string().uuid().optional(),
});

export const checkPermissionsSchema = z.object({
  permissions: z.array(permissionSchema).min(1),
  userId: z.string().uuid().optional(),
  requireAll: z.boolean().default(false),
});

// Query schemas
export const roleQuerySchema = z.object({
  organizationId: z.string().uuid('Invalid organization ID'),
  includeSystemRoles: z.boolean().default(true),
  includePermissions: z.boolean().default(true),
});

export const userRoleQuerySchema = z.object({
  organizationId: z.string().uuid('Invalid organization ID'),
  userId: z.string().uuid().optional(),
  includeExpired: z.boolean().default(false),
});

export const auditLogQuerySchema = z.object({
  organizationId: z.string().uuid('Invalid organization ID'),
  userId: z.string().uuid().optional(),
  action: z.enum(['assigned', 'removed', 'modified', 'created', 'deleted']).optional(),
  limit: z.number().int().min(1).max(100).default(50),
  offset: z.number().int().min(0).default(0),
});

export const teamMembersQuerySchema = z.object({
  organizationId: z.string().uuid('Invalid organization ID'),
  includeInactive: z.boolean().default(false),
  includeRoles: z.boolean().default(true),
});

// Team member management schemas
export const updateTeamMemberSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  role: z.enum(['owner', 'admin', 'manager', 'staff']).optional(),
  isActive: z.boolean().optional(),
});

export const removeTeamMemberSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
});

// Validation helper functions
export const validatePermissions = (permissions: unknown) => {
  return z.array(permissionSchema).safeParse(permissions);
};

export const validateRole = (role: unknown) => {
  return createRoleSchema.safeParse(role);
};

export const validateUserRole = (userRole: unknown) => {
  return assignRoleSchema.safeParse(userRole);
};

export const validateInvitation = (invitation: unknown) => {
  return inviteTeamMemberSchema.safeParse(invitation);
};

export const validateCreateTeamMember = (teamMemberData: unknown) => {
  return createTeamMemberSchema.safeParse(teamMemberData);
};

export const validateEnhancedInvitation = (invitation: unknown) => {
  return enhancedInviteTeamMemberSchema.safeParse(invitation);
};

// Type exports for the schemas
export type PermissionInput = z.infer<typeof permissionSchema>;
export type CreateRoleInput = z.infer<typeof createRoleSchema>;
export type UpdateRoleInput = z.infer<typeof updateRoleSchema>;
export type AssignRoleInput = z.infer<typeof assignRoleSchema>;
export type RemoveRoleInput = z.infer<typeof removeRoleSchema>;
export type BulkAssignRolesInput = z.infer<typeof bulkAssignRolesSchema>;
export type InviteTeamMemberInput = z.infer<typeof inviteTeamMemberSchema>;
export type AcceptInvitationInput = z.infer<typeof acceptInvitationSchema>;
export type CheckPermissionInput = z.infer<typeof checkPermissionSchema>;
export type CheckPermissionsInput = z.infer<typeof checkPermissionsSchema>;
export type RoleQueryInput = z.infer<typeof roleQuerySchema>;
export type UserRoleQueryInput = z.infer<typeof userRoleQuerySchema>;
export type AuditLogQueryInput = z.infer<typeof auditLogQuerySchema>;
export type TeamMembersQueryInput = z.infer<typeof teamMembersQuerySchema>;
export type UpdateTeamMemberInput = z.infer<typeof updateTeamMemberSchema>;
export type RemoveTeamMemberInput = z.infer<typeof removeTeamMemberSchema>;

export type LoginFormData = z.infer<typeof loginSchema>
export type RegisterFormData = z.infer<typeof registerSchema>
export type InviteTeamMemberFormData = z.infer<typeof inviteTeamMemberSchema>
export type CreateTeamMemberFormData = z.infer<typeof createTeamMemberSchema>
export type EnhancedInviteTeamMemberFormData = z.infer<typeof enhancedInviteTeamMemberSchema> 