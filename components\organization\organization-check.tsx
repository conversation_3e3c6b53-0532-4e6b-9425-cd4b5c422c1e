'use client'

import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/lib/contexts/auth-context'

interface OrganizationCheckProps {
  children: React.ReactNode
  excludePaths?: string[] // Paths to exclude from redirect logic
}

export function OrganizationCheck({ children, excludePaths = [] }: OrganizationCheckProps) {
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const { user } = useAuth()

  const checkOrganization = useCallback(async () => {
    try {
      // First check if user already has organization_id in their profile
      if (user?.organization_id) {
        setIsLoading(false)
        return
      }

      const supabase = createClient()
      
      // If not in user context, check database
      const { data: userData } = await supabase
        .from('users')
        .select(`
          organization_id,
          organizations (
            id,
            name,
            slug,
            subscription_status
          )
        `)
        .eq('id', user!.id)
        .single()

      const hasOrg = !!(userData?.organizations && userData.organization_id)

      // If no organization and not on excluded paths, redirect to onboarding
      if (!hasOrg && user) {
        const currentPath = window.location.pathname
        const isExcludedPath = excludePaths.some(path => currentPath.startsWith(path))
        
        if (!isExcludedPath && !currentPath.startsWith('/onboarding')) {
          router.push('/onboarding/organization')
          return
        }
      }
    } catch (error) {
      console.error('Error checking organization:', error)
      // On error, assume no organization and redirect
      if (user && !window.location.pathname.startsWith('/onboarding')) {
        router.push('/onboarding/organization')
      }
    } finally {
      setIsLoading(false)
    }
  }, [user, router, excludePaths])

  useEffect(() => {
    if (!user) {
      setIsLoading(false)
      return
    }

    checkOrganization()
  }, [user, checkOrganization])

  // Show loading state while checking
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center space-y-4">
          <div className="h-8 w-8 border-4 border-orange-200 border-t-orange-600 rounded-full animate-spin mx-auto"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // If user has no organization and is not on excluded paths, we've already redirected
  // This component will only render children if the user has an organization or is on an excluded path
  return <>{children}</>
} 